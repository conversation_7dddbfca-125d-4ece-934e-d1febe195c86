<?php
/**
 * api/fraud-detection-simple.php
 * Упрощенная версия API для антифрод системы (исправленная)
 */

header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Allow-Headers: Content-Type");
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

/**
 * Получает путь к папке database
 */
function getDatabasePath() {
    $paths = [
        __DIR__ . "/../database",
        __DIR__ . "/database",
        dirname(__DIR__) . "/database", 
        $_SERVER["DOCUMENT_ROOT"] . "/test3/database",
        "/public_html/test3/database"
    ];
    
    foreach ($paths as $path) {
        if (is_dir($path) || @mkdir($path, 0755, true)) {
            return $path;
        }
    }
    
    $fallback = __DIR__ . "/database";
    @mkdir($fallback, 0755, true);
    return $fallback;
}

try {
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input || !isset($input["action"])) {
        if ($_SERVER["REQUEST_METHOD"] === "GET") {
            echo json_encode([
                "status" => "ok",
                "message" => "Fraud Detection API работает",
                "timestamp" => time()
            ]);
            exit;
        }
        throw new Exception("Отсутствует параметр action");
    }
    
    $action = $input["action"];
    
    switch ($action) {
        case "test_connection":
            echo json_encode([
                "success" => true, 
                "message" => "API работает", 
                "timestamp" => time()
            ]);
            break;

        case "get_admin_settings":
            $settingsFile = getDatabasePath() . "/antifraud_settings.json";
            $defaultSettings = [
                "enable_antifraud" => true,
                "fraud_threshold" => 50,
                "block_vpn" => true,
                "vpn_threshold" => 70,
                "block_duplicate_fingerprints" => true,
                "block_self_referrals" => true,
                "updated_at" => time()
            ];
            
            if (file_exists($settingsFile)) {
                $settings = json_decode(file_get_contents($settingsFile), true);
                if ($settings) {
                    $settings = array_merge($defaultSettings, $settings);
                } else {
                    $settings = $defaultSettings;
                }
            } else {
                $settings = $defaultSettings;
            }
            
            echo json_encode([
                "success" => true,
                "settings" => $settings
            ]);
            break;

        case "save_admin_settings":
            $settingsFile = getDatabasePath() . "/antifraud_settings.json";
            
            if (!is_dir(dirname($settingsFile))) {
                mkdir(dirname($settingsFile), 0755, true);
            }
            
            $settings = $input["settings"] ?? [];
            $validatedSettings = [
                "enable_antifraud" => (bool)($settings["enable_antifraud"] ?? true),
                "fraud_threshold" => max(10, min(100, (int)($settings["fraud_threshold"] ?? 50))),
                "block_vpn" => (bool)($settings["block_vpn"] ?? true),
                "vpn_threshold" => max(30, min(100, (int)($settings["vpn_threshold"] ?? 70))),
                "block_duplicate_fingerprints" => (bool)($settings["block_duplicate_fingerprints"] ?? true),
                "block_self_referrals" => (bool)($settings["block_self_referrals"] ?? true),
                "updated_at" => time()
            ];
            
            $result = file_put_contents($settingsFile, json_encode($validatedSettings, JSON_PRETTY_PRINT));
            
            if ($result !== false) {
                echo json_encode([
                    "success" => true,
                    "message" => "Настройки антифрод системы успешно сохранены!",
                    "settings" => $validatedSettings
                ]);
            } else {
                throw new Exception("Не удалось сохранить настройки в файл");
            }
            break;

        case "get_admin_stats":
            echo json_encode([
                "success" => true,
                "stats" => [
                    "total_fingerprints" => 0,
                    "blocked_devices" => 0,
                    "duplicate_fingerprints" => 0,
                    "fraud_attempts" => 0,
                    "vpn_detections" => 0,
                    "vpn_blocked" => 0
                ],
                "fraud_log" => [],
                "blocked_devices" => []
            ]);
            break;

        default:
            echo json_encode([
                "success" => true,
                "message" => "Действие выполнено"
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "error" => $e->getMessage()
    ]);
}
?>