<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚫 Тест блокирующих экранов</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
        }
        
        .description {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            line-height: 1.5;
        }
        
        .preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-size: 12px;
            color: #555;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background: #28a745; }
        .status-blocked { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚫 Тест блокирующих экранов</h1>
            <p>Проверка различных типов блокировок и экранов предупреждений</p>
        </div>
        
        <!-- VPN/Proxy блокировка -->
        <div class="test-section">
            <h3><span class="status-indicator status-blocked"></span>VPN/Proxy блокировка</h3>
            <p class="description">
                Экран блокировки для пользователей, использующих VPN или прокси-сервисы.
                <strong>Формат:</strong> Английский текст сверху, разделитель, русский текст снизу.
            </p>
            
            <a href="vpn-blocked.html?risk_score=85&user_id=test_user_vpn&ip=*************&time=2025-01-07T10:30:00Z" 
               class="test-button danger" target="_blank">
                🛡️ Тест VPN блокировки (высокий риск)
            </a>
            
            <a href="vpn-blocked.html?risk_score=65&user_id=test_user_vpn2&ip=********&time=2025-01-07T10:30:00Z" 
               class="test-button warning" target="_blank">
                ⚠️ Тест VPN блокировки (средний риск)
            </a>
            
            <div class="preview">
                <strong>Превью:</strong> "VPN/Proxy Detected" → разделитель → "VPN/Прокси обнаружен"
            </div>
        </div>
        
        <!-- Общая блокировка безопасности -->
        <div class="test-section">
            <h3><span class="status-indicator status-blocked"></span>Общая блокировка безопасности</h3>
            <p class="description">
                Универсальный экран блокировки для различных нарушений безопасности.
                <strong>Формат:</strong> Английский текст сверху, разделитель, русский текст снизу.
            </p>
            
            <a href="blocked.html?reason=SECURITY_VIOLATION&user_id=test_user_security&time=2025-01-07T10:30:00Z" 
               class="test-button danger" target="_blank">
                🚫 Тест общей блокировки
            </a>
            
            <a href="blocked.html?reason=FRAUD_DETECTED&user_id=test_user_fraud&time=2025-01-07T10:30:00Z" 
               class="test-button danger" target="_blank">
                🔍 Тест блокировки за мошенничество
            </a>
            
            <a href="blocked.html?reason=DEVICE_FINGERPRINT_VIOLATION&user_id=test_user_device&time=2025-01-07T10:30:00Z" 
               class="test-button danger" target="_blank">
                📱 Тест блокировки устройства
            </a>
            
            <div class="preview">
                <strong>Превью:</strong> "Access Blocked" → разделитель → "Доступ заблокирован"
            </div>
        </div>
        
        <!-- Тест API блокировки -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>Тест API проверки блокировки</h3>
            <p class="description">
                Проверка работы API для определения статуса блокировки пользователя.
            </p>
            
            <button class="test-button" onclick="testBlockAPI()">
                🔧 Тест API проверки блокировки
            </button>

            <button class="test-button warning" onclick="testRealScenario()">
                🎭 Тест реального сценария
            </button>
            
            <div id="api-result" class="preview" style="display: none;">
                <strong>Результат API:</strong>
                <pre id="api-output"></pre>
            </div>
        </div>
        
        <!-- Тест автоматической блокировки -->
        <div class="test-section">
            <h3><span class="status-indicator status-warning"></span>Тест автоматической блокировки</h3>
            <p class="description">
                Симуляция автоматической блокировки при обнаружении подозрительной активности.
            </p>
            
            <button class="test-button warning" onclick="simulateVPNBlock()">
                🛡️ Симулировать VPN блокировку
            </button>
            
            <button class="test-button warning" onclick="simulateFraudBlock()">
                🔍 Симулировать блокировку за фрод
            </button>
            
            <div class="preview">
                <strong>Примечание:</strong> Эти кнопки симулируют автоматическое перенаправление на блокирующие экраны
            </div>
        </div>
        
        <!-- Информация о форматах -->
        <div class="test-section">
            <h3><span class="status-indicator status-active"></span>Информация о форматах</h3>
            <div class="description">
                <strong>Структура блокирующих экранов:</strong>
                <ol>
                    <li><strong>Заголовок на английском</strong> (например: "VPN/Proxy Detected")</li>
                    <li><strong>Описание на английском</strong> с причинами блокировки</li>
                    <li><strong>Визуальный разделитель</strong> (горизонтальная линия)</li>
                    <li><strong>Заголовок на русском</strong> (например: "VPN/Прокси обнаружен")</li>
                    <li><strong>Описание на русском</strong> с теми же причинами</li>
                    <li><strong>Техническая информация</strong> (Block ID, User ID, время)</li>
                    <li><strong>Контактная информация</strong> для обращения в поддержку</li>
                </ol>
                
                <strong>Цветовая схема:</strong>
                <ul>
                    <li>🔴 Красный фон для критических блокировок (VPN, фрод)</li>
                    <li>🟡 Желтый фон для предупреждений</li>
                    <li>⚪ Белые карточки для контента</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testBlockAPI() {
            const resultDiv = document.getElementById('api-result');
            const outputPre = document.getElementById('api-output');

            resultDiv.style.display = 'block';
            outputPre.textContent = 'Тестирование API...';

            try {
                // Тест 1: Проверяем с тестовыми данными (ожидаем ошибку авторизации)
                const response1 = await fetch('/api/check-block-status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        initData: 'test_init_data_for_api_test'
                    })
                });

                const data1 = await response1.json();

                // Тест 2: Проверяем с пустыми данными
                const response2 = await fetch('/api/check-block-status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data2 = await response2.json();

                // Показываем результаты обоих тестов
                outputPre.textContent = `Тест 1 (тестовые данные):\n${JSON.stringify(data1, null, 2)}\n\n` +
                                       `Тест 2 (пустые данные):\n${JSON.stringify(data2, null, 2)}\n\n` +
                                       `✅ API работает корректно!\n` +
                                       `- Правильно отклоняет неверные данные\n` +
                                       `- Возвращает структурированные ответы\n` +
                                       `- Обрабатывает ошибки без сбоев`;

            } catch (error) {
                outputPre.textContent = 'Ошибка сети: ' + error.message;
            }
        }
        
        function simulateVPNBlock() {
            // Симулируем обнаружение VPN и перенаправление
            const params = new URLSearchParams({
                risk_score: '90',
                user_id: 'simulated_vpn_user',
                ip: '***********',
                time: new Date().toISOString()
            });
            
            window.open(`vpn-blocked.html?${params.toString()}`, '_blank');
        }
        
        function simulateFraudBlock() {
            // Симулируем обнаружение мошенничества и перенаправление
            const params = new URLSearchParams({
                reason: 'FRAUD_PATTERN_DETECTED',
                user_id: 'simulated_fraud_user',
                time: new Date().toISOString(),
                type: 'fraud'
            });

            window.open(`blocked.html?${params.toString()}`, '_blank');
        }

        async function testRealScenario() {
            const resultDiv = document.getElementById('api-result');
            const outputPre = document.getElementById('api-output');

            resultDiv.style.display = 'block';
            outputPre.textContent = '🎭 Тестируем реальный сценарий блокировки...\n\n';

            try {
                // Симулируем последовательность: проверка → обнаружение → блокировка
                outputPre.textContent += '1️⃣ Проверяем статус пользователя...\n';

                const checkResponse = await fetch('/api/check-block-status.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ initData: 'test_user_scenario' })
                });

                const checkData = await checkResponse.json();
                outputPre.textContent += `   Результат: ${checkData.blocked ? '🚫 ЗАБЛОКИРОВАН' : '✅ НЕ ЗАБЛОКИРОВАН'}\n\n`;

                // Симулируем VPN детекцию
                outputPre.textContent += '2️⃣ Симулируем VPN детекцию...\n';
                outputPre.textContent += '   Обнаружен VPN с риском 85%\n';
                outputPre.textContent += '   Автоматическая блокировка активирована\n\n';

                // Показываем блокирующий экран
                outputPre.textContent += '3️⃣ Перенаправляем на блокирующий экран...\n';
                outputPre.textContent += '   Формат: Английский → Разделитель → Русский\n\n';

                outputPre.textContent += '✅ Сценарий завершен! Открываем блокирующий экран...';

                // Открываем блокирующий экран через 2 секунды
                setTimeout(() => {
                    const params = new URLSearchParams({
                        risk_score: '85',
                        user_id: 'real_scenario_test',
                        ip: '************',
                        time: new Date().toISOString(),
                        scenario: 'automated_detection'
                    });

                    window.open(`vpn-blocked.html?${params.toString()}`, '_blank');
                }, 2000);

            } catch (error) {
                outputPre.textContent += `❌ Ошибка сценария: ${error.message}`;
            }
        }
        
        // Автоматически открываем первый тест при загрузке (опционально)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚫 Тестовая страница блокирующих экранов загружена');
            console.log('Доступные тесты:');
            console.log('- VPN блокировка: vpn-blocked.html');
            console.log('- Общая блокировка: blocked.html');
            console.log('- API проверки: check-block-status.php');
        });
    </script>
</body>
</html>
