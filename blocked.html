<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Blocked</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .blocked-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .blocked-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .blocked-title {
            font-size: 28px;
            font-weight: bold;
            color: #e53e3e;
            margin-bottom: 15px;
        }
        
        .blocked-subtitle {
            font-size: 18px;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .blocked-message {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            color: #742a2a;
            line-height: 1.6;
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 30px 0;
        }
        
        .blocked-message-ru {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 10px;
            padding: 20px;
            color: #234e52;
            line-height: 1.6;
        }
        
        .reason-code {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            color: #4a5568;
        }
        
        .contact-info {
            margin-top: 30px;
            padding: 20px;
            background: #f0fff4;
            border-radius: 10px;
            border: 1px solid #9ae6b4;
        }
        
        .contact-info h4 {
            color: #22543d;
            margin-bottom: 10px;
        }
        
        .contact-info p {
            color: #2f855a;
            margin: 5px 0;
        }
        
        .timestamp {
            margin-top: 20px;
            font-size: 12px;
            color: #718096;
        }
        
        .retry-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            margin-top: 20px;
        }
        
        .retry-button:hover {
            transform: translateY(-2px);
        }
        
        .warning-badge {
            display: inline-block;
            background: #fed7d7;
            color: #742a2a;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="blocked-container">
        <div class="blocked-icon">🚫</div>
        
        <div class="warning-badge">SECURITY ALERT</div>
        
        <h1 class="blocked-title">Access Blocked</h1>
        <p class="blocked-subtitle">Your access has been restricted</p>
        
        <div class="blocked-message">
            <strong>🔒 Access Denied</strong><br>
            Your account has been temporarily blocked due to security policy violations. This may be due to:
            <ul style="text-align: left; margin-top: 10px;">
                <li>VPN/Proxy usage detected</li>
                <li>Suspicious device fingerprint</li>
                <li>Fraudulent activity patterns</li>
                <li>Multiple account violations</li>
            </ul>
        </div>
        
        <div class="divider"></div>
        
        <div class="blocked-message-ru">
            <strong>🔒 Доступ заблокирован</strong><br>
            Ваш аккаунт временно заблокирован из-за нарушения политики безопасности. Это может быть связано с:
            <ul style="text-align: left; margin-top: 10px;">
                <li>Обнаружено использование VPN/Proxy</li>
                <li>Подозрительный отпечаток устройства</li>
                <li>Мошенническая активность</li>
                <li>Множественные нарушения аккаунта</li>
            </ul>
        </div>
        
        <div class="reason-code">
            <strong>Block Reason:</strong> <span id="blockReason">SECURITY_VIOLATION</span><br>
            <strong>Block ID:</strong> <span id="blockId">BLK-001</span><br>
            <strong>User ID:</strong> <span id="userId">Unknown</span>
        </div>
        
        <div class="contact-info">
            <h4>📞 Need Help?</h4>
            <p><strong>Support:</strong> @uniqpaid_support</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Appeal:</strong> Include your Block ID in the message</p>
        </div>
        
        <button class="retry-button" onclick="retryAccess()">
            🔄 Retry Access
        </button>
        
        <div class="timestamp">
            Blocked at: <span id="blockTime"></span>
        </div>
    </div>

    <script>
        // Получаем параметры из URL
        const urlParams = new URLSearchParams(window.location.search);
        const reason = urlParams.get('reason') || 'SECURITY_VIOLATION';
        const userId = urlParams.get('user_id') || 'Unknown';
        const blockTime = urlParams.get('time') || new Date().toISOString();
        
        // Обновляем информацию на странице
        document.getElementById('blockReason').textContent = reason;
        document.getElementById('blockId').textContent = 'BLK-' + Date.now().toString().slice(-6);
        document.getElementById('userId').textContent = userId;
        document.getElementById('blockTime').textContent = new Date(blockTime).toLocaleString();
        
        // Функция повторной попытки доступа
        function retryAccess() {
            // Перенаправляем обратно в приложение
            if (window.Telegram && window.Telegram.WebApp) {
                window.Telegram.WebApp.close();
            } else {
                window.location.href = '/';
            }
        }
        
        // Логируем попытку доступа заблокированного пользователя
        fetch('/api/fraud-detection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'log_suspicious_activity',
                activity_type: 'blocked_user_access_attempt',
                details: {
                    user_id: userId,
                    reason: reason,
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent,
                    referrer: document.referrer
                }
            })
        }).catch(e => console.log('Logging failed:', e));
    </script>
</body>
</html>
