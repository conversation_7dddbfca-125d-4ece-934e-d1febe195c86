# 🔧 МИНИМАЛЬНАЯ ВЕРСИЯ getUserData.php

## ❌ **Проблема:**
Сложная версия `getUserData.php` продолжает давать ошибку 500, несмотря на все исправления.

## ✅ **Решение:**
Создана **МИНИМАЛЬНАЯ** версия, которая делает только самое необходимое:

### **Что делает минимальная версия:**
1. ✅ Проверяет метод POST
2. ✅ Читает initData из запроса
3. ✅ Извлекает ID пользователя
4. ✅ Возвращает базовый JSON ответ
5. ✅ **НЕ подключает** никаких зависимостей
6. ✅ **НЕ использует** сложную валидацию
7. ✅ **НЕ работает** с файлами базы данных

### **Код минимальной версии:**
```php
<?php
header('Content-Type: application/json');

try {
    // Проверяем метод
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Только POST']);
        exit;
    }
    
    // Читаем данные
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Нет initData']);
        exit;
    }
    
    // Извлекаем user ID из initData
    parse_str($input['initData'], $parts);
    $user = json_decode($parts['user'] ?? '{}', true);
    
    if (!$user || !isset($user['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Нет ID пользователя']);
        exit;
    }
    
    $userId = intval($user['id']);
    
    // Возвращаем минимальный ответ
    echo json_encode([
        'userId' => $userId,
        'balance' => 0,
        'min_withdrawal' => 0,
        'min_balance_for_withdrawal' => 100,
        'withdrawals_count' => 0,
        'test' => true
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
```

## 🚀 **Инструкция:**

1. **Загрузи обновленный** `api/getUserData.php` на продакшен
2. **Очисти кэш Telegram** (закрой → очисти → перезапусти → /start)
3. **Проверь работу** - должен исчезнуть HTTP 500

## ✅ **Ожидаемый результат:**

- ✅ Нет ошибки 500
- ✅ Приложение загружается
- ✅ Показывается ID пользователя из Telegram
- ✅ Баланс = 0 (тестовое значение)
- ✅ В ответе есть `"test": true`

## 🔄 **После проверки работы:**

Если минимальная версия работает, значит проблема была в:
- Подключении зависимостей (config.php, validate_initdata.php, db_mock.php)
- Сложной валидации
- Работе с файлами базы данных

Тогда можно будет постепенно добавлять функциональность обратно.

## 📞 **Если минимальная версия тоже не работает:**

Значит проблема в:
- Настройках PHP на сервере
- Правах доступа к файлам
- Конфигурации веб-сервера

В этом случае нужно проверить логи сервера.

## 🎯 **Цель:**

Сначала заставить работать **ХОТЬ ЧТО-ТО**, а потом добавлять функциональность по частям.

Минимальная версия должна работать на 100%! 🚀
