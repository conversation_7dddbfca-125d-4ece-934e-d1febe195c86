# 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМ С API ПУТЯМИ

## ❌ **Найденные проблемы:**

### 1. **Неправильные пути к API**
- **Проблема:** `API_BASE_URL = "./api"` (относительный путь)
- **В Telegram WebApp:** Относительные пути работают неправильно
- **Результат:** 500 ошибки при обращении к API

### 2. **Прямые fetch запросы вместо ApiClient**
- **Проблема:** `main.js` делал прямой `fetch()` вместо использования `ApiClient`
- **В ApiClient:** Есть правильная логика определения URL
- **Результат:** Обход системы правильных путей

### 3. **Отсутствие определения окружения**
- **Проблема:** Один путь для локальной разработки и продакшена
- **Нужно:** Разные пути для разных окружений

## ✅ **Исправления:**

### 1. **Исправлен config.js**
```javascript
// БЫЛО:
static API_BASE_URL = "./api";

// СТАЛО:
static API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname.includes('argun-defolt.loc') 
  ? "./api" 
  : "https://app.uniqpaid.com/test3/api";
```

### 2. **Исправлен main.js**
```javascript
// БЫЛО:
const response = await fetch(`${window.API_BASE_URL}/getUserData.php`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ initData: initData }),
});

// СТАЛО:
const data = await window.apiClient.request('getUserData.php', {
  body: { initData: initData }
});
```

### 3. **Исправлен api-client.js**
```javascript
// БЫЛО:
this.baseUrl = window.API_BASE_URL || 'https://app.uniqpaid.com/test3/api';

// СТАЛО:
if (window.API_BASE_URL) {
  this.baseUrl = window.API_BASE_URL;
} else if (window.location.hostname === 'localhost' || window.location.hostname.includes('argun-defolt.loc')) {
  this.baseUrl = './api';
} else {
  this.baseUrl = 'https://app.uniqpaid.com/test3/api';
}
```

### 4. **Исправлен ads-config.js**
```javascript
// БЫЛО:
BASE_URL: './api',

// СТАЛО:
BASE_URL: window.location.hostname === 'localhost' || window.location.hostname.includes('argun-defolt.loc') 
  ? './api' 
  : 'https://app.uniqpaid.com/test3/api',
```

## 🎯 **Логика определения окружения:**

### **Локальная разработка:**
- `localhost` или `argun-defolt.loc` → `"./api"`
- Относительные пути работают нормально

### **Продакшен:**
- `app.uniqpaid.com` → `"https://app.uniqpaid.com/test3/api"`
- Абсолютные пути для Telegram WebApp

## 📁 **Файлы для загрузки на продакшен:**

1. `js/config.js` (исправлены пути API)
2. `js/main.js` (использует ApiClient)
3. `js/api-client.js` (улучшенное определение URL)
4. `js/ads-config.js` (исправлены пути API)

## 🚀 **Инструкция по развертыванию:**

1. **Загрузи все исправленные файлы** на продакшен
2. **Очисти кэш Telegram** (закрой → очисти кэш → перезапусти → /start)
3. **Проверь консоль** - не должно быть ошибок 500

## ✅ **Ожидаемый результат:**

После исправлений:
- ✅ Нет ошибок 500 при обращении к API
- ✅ Правильные пути в зависимости от окружения
- ✅ Использование централизованного ApiClient
- ✅ Загрузка реальных данных пользователя
- ✅ Работа всех API запросов

## 🔍 **Для проверки:**

1. **В консоли браузера должно быть:**
   ```
   [ApiClient] Запрос 1: POST https://app.uniqpaid.com/test3/api/getUserData.php
   [ApiClient] Успешный ответ от getUserData.php
   ```

2. **Не должно быть:**
   ```
   Failed to load resource: the server responded with a status of 500
   ```

## 📞 **Если проблемы остаются:**

1. Убедись, что все файлы загружены правильно
2. Проверь, что кэш Telegram очищен
3. Открой консоль и посмотри, какие URL используются
4. Проверь, что `window.apiClient` доступен

Теперь API должно работать правильно! 🎯
