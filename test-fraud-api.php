<?php
/**
 * test-fraud-api.php
 * Тест API антифрод системы
 */

echo "<h1>🔍 Тест API антифрод системы</h1>";

// Тест 1: Проверка соединения
echo "<h2>Тест 1: Проверка соединения</h2>";
$testUrl = '/api/fraud-detection.php';

$testData = [
    'action' => 'test_connection'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost' . $testUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✅ Тест соединения прошел успешно!</p>";
    } else {
        echo "<p style='color: red;'>❌ Ошибка в ответе API</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Ошибка HTTP: $httpCode</p>";
}

// Тест 2: Получение настроек
echo "<h2>Тест 2: Получение настроек антифрод</h2>";

$testData2 = [
    'action' => 'get_admin_settings'
];

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, 'http://localhost' . $testUrl);
curl_setopt($ch2, CURLOPT_POST, true);
curl_setopt($ch2, CURLOPT_POSTFIELDS, json_encode($testData2));
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_TIMEOUT, 10);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "<p><strong>HTTP Code:</strong> $httpCode2</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response2) . "</pre>";

if ($httpCode2 === 200) {
    $data2 = json_decode($response2, true);
    if ($data2 && $data2['success']) {
        echo "<p style='color: green;'>✅ Получение настроек прошло успешно!</p>";
    } else {
        echo "<p style='color: red;'>❌ Ошибка в ответе API</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Ошибка HTTP: $httpCode2</p>";
}

// Тест 3: Получение статистики
echo "<h2>Тест 3: Получение статистики</h2>";

$testData3 = [
    'action' => 'get_admin_stats'
];

$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, 'http://localhost' . $testUrl);
curl_setopt($ch3, CURLOPT_POST, true);
curl_setopt($ch3, CURLOPT_POSTFIELDS, json_encode($testData3));
curl_setopt($ch3, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_TIMEOUT, 10);

$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "<p><strong>HTTP Code:</strong> $httpCode3</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response3) . "</pre>";

if ($httpCode3 === 200) {
    $data3 = json_decode($response3, true);
    if ($data3 && $data3['success']) {
        echo "<p style='color: green;'>✅ Получение статистики прошло успешно!</p>";
    } else {
        echo "<p style='color: red;'>❌ Ошибка в ответе API</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Ошибка HTTP: $httpCode3</p>";
}

// Тест 4: Прямой вызов файла
echo "<h2>Тест 4: Прямой вызов файла</h2>";

try {
    // Эмулируем POST запрос
    $_POST = [];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Эмулируем входные данные
    $testInput = json_encode(['action' => 'test_connection']);
    
    // Временно перенаправляем вывод
    ob_start();
    
    // Эмулируем php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'test_input');
    file_put_contents($tempFile, $testInput);
    
    echo "<p>Попытка прямого вызова...</p>";
    
    // Включаем файл
    include 'api/fraud-detection.php';
    
    $output = ob_get_clean();
    
    echo "<p><strong>Прямой вывод:</strong></p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    unlink($tempFile);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Ошибка прямого вызова: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>📋 Итоги тестирования</h2>";
echo "<p>Проверьте результаты выше. Если есть ошибки 500, проверьте логи сервера.</p>";
echo "<p><strong>Файлы для проверки:</strong></p>";
echo "<ul>";
echo "<li>api/fraud-detection.php</li>";
echo "<li>api/config.php</li>";
echo "<li>api/db_mock.php</li>";
echo "<li>api/security.php</li>";
echo "<li>api/validate_initdata.php</li>";
echo "</ul>";
?>
