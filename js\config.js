// === config.js ===
// Файл: js/config.js
// Описание: Централизованная конфигурация и константы приложения UniQPaid.

// Проверяем, не загружен ли уже класс
if (typeof window.AppConfig !== 'undefined') {
  console.warn('[Config] AppConfig уже загружен, пропускаем повторную инициализацию');
} else {

class AppConfig {
  // --- API и внешние сервисы ---
  static API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname.includes('argun-defolt.loc')
    ? "./api"
    : "https://app.uniqpaid.com/test3/api";
  static MY_PUB_ID = "944840";
  static MY_APP_ID = "2122";
  static DEBUG_MODE = false;
  static BOT_USERNAME = "uniqpaid_paid_bot"; // Имя бота (без @)

  // --- Настройки UI и анимаций ---
  static PAGE_TRANSITION_DURATION = 300; // мс, для CSS (должно совпадать)

  // --- Настройки поведения рекламы ---
  static AUTO_RELOAD_AFTER_COUNTDOWN = true; // 🔄 Автоматический reload после таймера для обновления рекламы
  static USE_SOFT_REFRESH = false; // 🔄 Использовать мягкое обновление (переинициализация SDK) вместо полного reload

  // --- Типы рекламы ---
  static AD_TYPES = {
    NATIVE_BANNER: 'native_banner',     // Баннер-превью (для верхней кнопки)
    INTERSTITIAL: 'interstitial',       // Полноэкранный баннер (для средней кнопки)
    REWARDED_VIDEO: 'rewarded_video'    // Видеореклама (для нижней кнопки)
  };

  // --- Настройки рекламы ---
  static AD_COOLDOWN_TIME = 3000; // 3 секунды между показами рекламы

  // --- Настройки вывода средств ---
  static MIN_WITHDRAWAL_AMOUNT = 0; // Нет минимальной суммы для вывода
  static MIN_BALANCE_FOR_WITHDRAWAL = 100; // Минимальный баланс для доступа к выводу

  // --- Экономические настройки ---
  static COIN_VALUE = 0.001; // $0.001 за монету (должно соответствовать серверному значению CONVERSION_RATE)

  // --- Данные о криптовалютах (базовые) ---
  static CURRENCY_DATA = {
    ton: {
      name: 'TON (Telegram)',
      minCoins: 1489, // ОБНОВЛЕНО: 1489 монет с учетом комиссии NOWPayments
      networkFee: 0.15, // Комиссия $0.15 (низкая комиссия TON)
      status: 'best'
    },
    trx: {
      name: 'TRON (TRX)',
      minCoins: 1500,
      networkFee: 1.0,
      status: 'good'
    },
    btc: {
      name: 'Bitcoin (BTC)',
      minCoins: 5000,
      networkFee: 5.0,
      status: 'expensive'
    },
    eth: {
      name: 'Ethereum (ETH)',
      minCoins: 3000,
      networkFee: 3.0,
      status: 'expensive'
    }
  };
}

class AppSettings {
  constructor() {
    this.settings = {
      show_fees_to_user: true,
      conversion_rate: 0.001, // Fallback значение, будет загружено с сервера
      ad_rewards: {}, // Награды будут загружаться с сервера
      min_balance_for_withdrawal: 100,
      min_withdrawal_amount: 0,
      ad_cooldowns: {
        'openLinkButton': 60,
        'watchVideoButton': 60,
        'openAdButton': 60
      }
    };
    this.currencyData = { ...AppConfig.CURRENCY_DATA };
    this.coinValue = 0.001; // Fallback значение, будет загружено с сервера

    // Загружаем актуальный курс конвертации при инициализации
    this.loadConversionRate();
  }

  update(newSettings) {
    if (newSettings) {
      Object.assign(this.settings, newSettings);

      // Обновляем coinValue из настроек
      if (newSettings.conversion_rate) {
        this.coinValue = newSettings.conversion_rate;
      }

      console.log('[AppSettings] Настройки обновлены:', this.settings);
    }
  }

  get(key) {
    return this.settings[key];
  }

  getAll() {
    return { ...this.settings };
  }

  getCoinValue() {
    return this.coinValue;
  }

  setCoinValue(value) {
    this.coinValue = value;
    this.settings.conversion_rate = value;
  }

  getCurrencyData(currency = null) {
    if (currency) {
      return this.currencyData[currency] || null;
    }
    return this.currencyData;
  }

  updateCurrencyData(newData) {
    if (newData) {
      Object.assign(this.currencyData, newData);
      console.log('[AppSettings] Данные валют обновлены:', this.currencyData);
    }
  }

  formatCurrency(value) {
    return parseFloat(value).toFixed(2);
  }

  /**
   * Загружает актуальный курс конвертации с сервера
   */
  async loadConversionRate() {
    try {
      console.log('[AppSettings] Загружаем актуальный курс конвертации...');
      const response = await fetch('api/get_conversion_rate.php');
      const data = await response.json();

      if (data.success) {
        const oldRate = this.coinValue;
        this.coinValue = data.conversion_rate;
        this.settings.conversion_rate = data.conversion_rate;

        // Обновляем дополнительные настройки
        if (data.settings) {
          Object.assign(this.settings, data.settings);
        }

        console.log(`[AppSettings] ✅ Курс конвертации обновлен: ${oldRate} → ${this.coinValue}`);
        console.log(`[AppSettings] Примеры: 1 монета = $${this.coinValue}, 1000 монет = $${this.coinValue * 1000}`);

        // Уведомляем другие компоненты об обновлении курса
        window.dispatchEvent(new CustomEvent('conversionRateUpdated', {
          detail: {
            oldRate: oldRate,
            newRate: this.coinValue,
            settings: data.settings
          }
        }));

        return data.conversion_rate;
      } else {
        console.warn('[AppSettings] Ошибка загрузки курса конвертации:', data.error);
        return this.coinValue; // Возвращаем fallback значение
      }
    } catch (error) {
      console.error('[AppSettings] Ошибка запроса курса конвертации:', error);
      return this.coinValue; // Возвращаем fallback значение
    }
  }

  /**
   * Принудительно обновляет курс конвертации
   */
  async refreshConversionRate() {
    return await this.loadConversionRate();
  }
}

// Экспорт в глобальную область
window.AppConfig = AppConfig;
window.appSettings = new AppSettings();

// Экспорт основных констант для обратной совместимости
window.API_BASE_URL = AppConfig.API_BASE_URL;
window.BOT_USERNAME = AppConfig.BOT_USERNAME;
window.PAGE_TRANSITION_DURATION = AppConfig.PAGE_TRANSITION_DURATION;
window.MY_PUB_ID = AppConfig.MY_PUB_ID;
window.MY_APP_ID = AppConfig.MY_APP_ID;
window.DEBUG_MODE = AppConfig.DEBUG_MODE;
window.AUTO_RELOAD_AFTER_COUNTDOWN = AppConfig.AUTO_RELOAD_AFTER_COUNTDOWN;
window.USE_SOFT_REFRESH = AppConfig.USE_SOFT_REFRESH;
window.AD_TYPES = AppConfig.AD_TYPES;

// Экспорт данных валют и настроек
window.currencyData = window.appSettings.getCurrencyData();
window.coinValue = window.appSettings.getCoinValue();

// Функция для форматирования чисел с двумя знаками после запятой (из оригинала)
window.formatCurrency = function(value) {
  return parseFloat(value).toFixed(2);
};

console.log('📦 [Config] Модуль конфигурации загружен с полной интеграцией.');

} // Закрывающая скобка для проверки дублирования