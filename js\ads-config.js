/**
 * js/ads-config.js
 * Централизованная конфигурация рекламной системы
 * Единый источник истины для всех настроек рекламы
 */

class AdsConfig {
  // ===== ОСНОВНЫЕ НАСТРОЙКИ RICHADS =====
  static RICHADS = {
    PUB_ID: "944840",
    APP_ID: "2122",
    DEBUG_MODE: false,
    SDK_URL: "https://richinfo.co/richpartners/telegram/js/tg-ob.js"
  };

  // ===== ТИПЫ РЕКЛАМЫ И МЕТОДЫ =====
  static AD_TYPES = {
    NATIVE_BANNER: {
      id: 'native_banner',
      method: 'triggerNativeNotification',
      params: true, // УЗЕНЬКИЙ БАННЕР СНИЗУ С АВТОПЕРЕХОДОМ - ПЕРВАЯ КНОПКА
      reward: 10, // fallback значение, будет обновлено с сервера
      buttonId: 'openLinkButton',
      counterId: 'native-banner-counter',
      icon: 'link',
      hasFallback: false, // БЕЗ fallback
      title: {
        ru: 'Открыть ссылку',
        en: 'Open Link'
      }
    },
    REWARDED_VIDEO: {
      id: 'rewarded_video',
      method: 'triggerInterstitialBanner',
      params: null, // ПОЛНОЭКРАННЫЙ ИНТЕРСТИЦИАЛЬНЫЙ БАННЕР - ВТОРАЯ КНОПКА
      reward: 1, // fallback значение, будет обновлено с сервера
      buttonId: 'watchVideoButton',
      counterId: 'rewarded-video-counter',
      icon: 'video',
      hasFallback: false, // БЕЗ fallback
      title: {
        ru: 'Смотреть видео',
        en: 'Watch Video'
      }
    },
    INTERSTITIAL: {
      id: 'interstitial',
      method: 'triggerNativeNotification',
      params: false, // УЗЕНЬКИЙ БАННЕР СНИЗУ БЕЗ АВТОПЕРЕХОДА - ТРЕТЬЯ КНОПКА
      reward: 10, // fallback значение, будет обновлено с сервера
      buttonId: 'openAdButton',
      counterId: 'interstitial-counter',
      icon: 'monitor',
      hasFallback: false, // БЕЗ fallback
      title: {
        ru: 'Открыть рекламу',
        en: 'Open Ad'
      }
    }
  };

  // ===== ЛИМИТЫ И ОГРАНИЧЕНИЯ =====
  static LIMITS = {
    DAILY_LIMIT_PER_TYPE: 20, // fallback значение, будет обновлено с сервера
    COOLDOWN_TIME: 20000, // 20 секунд
    MAX_RETRIES: 3,
    TIMEOUT: 30000 // 30 секунд таймаут для показа рекламы
  };

  // ===== НАСТРОЙКИ UI =====
  static UI = {
    COUNTDOWN_INTERVAL: 1000, // 1 секунда
    ANIMATION_DURATION: 300,
    BUTTON_STATES: {
      READY: 'ready',
      LOADING: 'loading', 
      COOLDOWN: 'cooldown',
      DISABLED: 'disabled',
      LIMIT_REACHED: 'limit_reached'
    },
    COUNTER_TEXTS: {
      // Используем ключи локализации вместо хардкода
      KEYS: {
        REMAINING: 'app.tasks.ad_views_left',
        REMAINING_SINGLE: 'app.tasks.ad_views_left_single',
        REMAINING_FEW: 'app.tasks.ad_views_left_few',
        LIMIT_REACHED: 'app.status.limit_reached',
        LOADING: 'app.status.loading',
        COOLDOWN: 'app.status.wait_before_next'
      },
      // Fallback тексты если локализация не загружена
      FALLBACK: {
        ru: {
          remaining: (count) => {
            if (count === 0) return 'лимит исчерпан';
            if (count === 1) return 'остался 1 показ';
            if (count >= 2 && count <= 4) return `осталось ${count} показа`;
            return `осталось ${count} показов`;
          },
          loading: 'загрузка...',
          cooldown: (seconds) => `подождите ${seconds}с`
        },
        en: {
          remaining: (count) => count === 0 ? 'limit reached' : `${count} ad views left`,
          loading: 'loading...',
          cooldown: (seconds) => `wait ${seconds}s`
        }
      }
    }
  };

  // ===== API ENDPOINTS =====
  static API = {
    BASE_URL: window.location.hostname === 'localhost' || window.location.hostname.includes('argun-defolt.loc')
      ? './api'
      : 'https://app.uniqpaid.com/test3/api',
    ENDPOINTS: {
      RECORD_AD_VIEW: 'ads-api.php?action=record_view',
      GET_LIMITS: 'ads-api.php?action=get_limits', 
      LOG_CLICK: 'ads-api.php?action=log_click',
      GET_STATS: 'ads-api.php?action=get_stats'
    }
  };

  // ===== СОБЫТИЯ =====
  static EVENTS = {
    AD_REQUESTED: 'ad:requested',
    AD_SHOWN: 'ad:shown', 
    AD_COMPLETED: 'ad:completed',
    AD_FAILED: 'ad:failed',
    AD_CLICKED: 'ad:clicked',
    LIMITS_UPDATED: 'limits:updated',
    COOLDOWN_STARTED: 'cooldown:started',
    COOLDOWN_ENDED: 'cooldown:ended'
  };

  // ===== СТАТУСЫ ЛОГИРОВАНИЯ =====
  static LOG_STATUS = {
    BUTTON_CLICK: 'button_click',
    AD_REQUEST: 'ad_request', 
    AD_SHOWN: 'ad_shown',
    AD_COMPLETED: 'ad_completed',
    AD_ERROR: 'ad_error',
    LIMIT_EXCEEDED: 'limit_exceeded',
    COOLDOWN_ACTIVE: 'cooldown_active',
    SUCCESS: 'success',
    ERROR: 'error'
  };

  // ===== МЕТОДЫ ПОЛУЧЕНИЯ КОНФИГУРАЦИИ =====
  
  /**
   * Получить конфигурацию типа рекламы по ID
   */
  static getAdType(adTypeId) {
    const adType = Object.values(this.AD_TYPES).find(type => type.id === adTypeId);

    // Обновляем награду из RewardBadgesManager если доступен
    if (adType && window.rewardBadgesManager && window.rewardBadgesManager.isInitialized) {
      const dynamicReward = window.rewardBadgesManager.getReward(adTypeId);
      if (dynamicReward > 0) {
        adType.reward = dynamicReward;
      }
    }

    return adType;
  }

  /**
   * Получить конфигурацию типа рекламы по ID кнопки
   */
  static getAdTypeByButton(buttonId) {
    return Object.values(this.AD_TYPES).find(type => type.buttonId === buttonId);
  }

  /**
   * Получить все типы рекламы как массив с актуальными наградами
   */
  static getAllAdTypes() {
    const adTypes = Object.values(this.AD_TYPES);

    // Обновляем награды из RewardBadgesManager если доступен
    if (window.rewardBadgesManager && window.rewardBadgesManager.isInitialized) {
      adTypes.forEach(adType => {
        const dynamicReward = window.rewardBadgesManager.getReward(adType.id);
        if (dynamicReward > 0) {
          adType.reward = dynamicReward;
        }
      });
    }

    return adTypes;
  }

  /**
   * Получить текст счетчика для языка с поддержкой локализации
   */
  static getCounterText(language, type, count) {
    // Простая и надежная система переводов
    if (type === 'remaining') {
      if (language === 'ru') {
        if (count === 0) return 'лимит исчерпан';
        if (count === 1) return 'остался 1 показ';
        if (count >= 2 && count <= 4) return `осталось ${count} показа`;
        return `осталось ${count} показов`;
      } else {
        if (count === 0) return 'limit reached';
        if (count === 1) return '1 ad view left';
        return `${count} ad views left`;
      }
    }

    if (type === 'loading') {
      return language === 'ru' ? 'загрузка...' : 'loading...';
    }

    if (type === 'cooldown') {
      return language === 'ru' ? `подождите ${count}с` : `wait ${count}s`;
    }

    return `${count}`;
  }

  /**
   * Получить полный URL API endpoint
   */
  static getApiUrl(endpoint) {
    return `${this.API.BASE_URL}/${this.API.ENDPOINTS[endpoint]}`;
  }

  /**
   * Проверить, достигнут ли лимит
   */
  static isLimitReached(currentCount) {
    return currentCount >= this.LIMITS.DAILY_LIMIT_PER_TYPE;
  }

  /**
   * Получить конфигурацию для PHP (для совместимости)
   */
  static getPhpConfig() {
    const config = {};
    
    // Награды для PHP
    this.getAllAdTypes().forEach(type => {
      config[`AD_REWARD_${type.id.toUpperCase()}`] = type.reward;
    });
    
    // Лимиты
    config.DAILY_LIMIT_PER_TYPE = this.LIMITS.DAILY_LIMIT_PER_TYPE;
    config.COOLDOWN_TIME = this.LIMITS.COOLDOWN_TIME;
    
    // RichAds
    config.RICHADS_PUB_ID = this.RICHADS.PUB_ID;
    config.RICHADS_APP_ID = this.RICHADS.APP_ID;
    
    return config;
  }

  /**
   * Валидация конфигурации
   */
  static validate() {
    const errors = [];
    
    // Проверяем RichAds настройки
    if (!this.RICHADS.PUB_ID || !this.RICHADS.APP_ID) {
      errors.push('RichAds PUB_ID и APP_ID обязательны');
    }
    
    // Проверяем типы рекламы
    this.getAllAdTypes().forEach(type => {
      if (!type.id || !type.method || !type.buttonId) {
        errors.push(`Неполная конфигурация для типа рекламы: ${type.id}`);
      }
      if (typeof type.reward !== 'number' || type.reward < 0) {
        errors.push(`Некорректная награда для типа: ${type.id}`);
      }
    });
    
    // Проверяем лимиты
    if (this.LIMITS.DAILY_LIMIT_PER_TYPE <= 0) {
      errors.push('Дневной лимит должен быть больше 0');
    }
    
    if (this.LIMITS.COOLDOWN_TIME < 0) {
      errors.push('Время cooldown не может быть отрицательным');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Обновить награды из данных сервера
   */
  static updateRewards(serverRewards) {
    if (!serverRewards || typeof serverRewards !== 'object') {
      console.warn('[AdsConfig] Некорректные данные наград:', serverRewards);
      return false;
    }

    let updated = false;

    // Обновляем награды в AD_TYPES
    if (serverRewards.native_banner && this.AD_TYPES.NATIVE_BANNER) {
      this.AD_TYPES.NATIVE_BANNER.reward = serverRewards.native_banner;
      updated = true;
    }

    if (serverRewards.rewarded_video && this.AD_TYPES.REWARDED_VIDEO) {
      this.AD_TYPES.REWARDED_VIDEO.reward = serverRewards.rewarded_video;
      updated = true;
    }

    if (serverRewards.interstitial && this.AD_TYPES.INTERSTITIAL) {
      this.AD_TYPES.INTERSTITIAL.reward = serverRewards.interstitial;
      updated = true;
    }

    if (updated) {
      console.log('[AdsConfig] ✅ Награды обновлены с сервера:', serverRewards);
    }

    return updated;
  }

  /**
   * Загрузить награды с сервера
   */
  static async loadRewardsFromServer() {
    try {
      const response = await fetch('api/get_ad_rewards.php');
      const data = await response.json();

      if (data.success) {
        this.updateRewards(data.rewards);

        // Также загружаем лимиты если они есть в ответе
        if (data.limits) {
          this.updateLimits(data.limits);
        }

        return data.rewards;
      } else {
        console.warn('[AdsConfig] Ошибка загрузки наград с сервера:', data.error);
        return null;
      }
    } catch (error) {
      console.warn('[AdsConfig] Ошибка запроса наград:', error);
      return null;
    }
  }

  /**
   * Обновить лимиты из данных сервера
   */
  static updateLimits(serverLimits) {
    if (!serverLimits || typeof serverLimits !== 'object') {
      console.warn('[AdsConfig] Некорректные данные лимитов:', serverLimits);
      return false;
    }

    let updated = false;

    // Обновляем общий лимит если есть
    if (serverLimits.daily_limit_per_type && typeof serverLimits.daily_limit_per_type === 'number') {
      this.LIMITS.DAILY_LIMIT_PER_TYPE = serverLimits.daily_limit_per_type;
      updated = true;
    }

    // Обновляем индивидуальные лимиты для каждого типа
    if (serverLimits.native_banner && typeof serverLimits.native_banner === 'number') {
      this.LIMITS.NATIVE_BANNER = serverLimits.native_banner;
      updated = true;
    }

    if (serverLimits.interstitial && typeof serverLimits.interstitial === 'number') {
      this.LIMITS.INTERSTITIAL = serverLimits.interstitial;
      updated = true;
    }

    if (serverLimits.rewarded_video && typeof serverLimits.rewarded_video === 'number') {
      this.LIMITS.REWARDED_VIDEO = serverLimits.rewarded_video;
      updated = true;
    }

    if (updated) {
      console.log('[AdsConfig] ✅ Лимиты обновлены с сервера:', serverLimits);
    }

    return updated;
  }

  /**
   * Получить лимит для конкретного типа рекламы
   */
  static getLimitForAdType(adTypeId) {
    // Сначала ищем индивидуальный лимит
    const individualLimit = this.LIMITS[adTypeId.toUpperCase()];
    if (individualLimit !== undefined) {
      return individualLimit;
    }

    // Иначе возвращаем общий лимит
    return this.LIMITS.DAILY_LIMIT_PER_TYPE;
  }
}

// Экспорт в глобальную область для обратной совместимости
window.AdsConfig = AdsConfig;

// Экспорт основных констант для совместимости с существующим кодом
window.MY_PUB_ID = AdsConfig.RICHADS.PUB_ID;
window.MY_APP_ID = AdsConfig.RICHADS.APP_ID;
window.AD_TYPES = Object.fromEntries(
  AdsConfig.getAllAdTypes().map(type => [type.id.toUpperCase(), type.id])
);

// Валидация при загрузке
const validation = AdsConfig.validate();
if (!validation.isValid) {
  console.error('[AdsConfig] Ошибки конфигурации:', validation.errors);
} else {
  console.log('[AdsConfig] ✅ Конфигурация рекламы загружена и валидна');
}

// Автоматически загружаем награды с сервера
AdsConfig.loadRewardsFromServer().then(rewards => {
  if (rewards) {
    console.log('[AdsConfig] 🏆 Награды загружены с сервера при инициализации');
  }
}).catch(error => {
  console.warn('[AdsConfig] Ошибка загрузки наград при инициализации:', error);
});

// Экспорт для совместимости (без ES6 modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdsConfig;
}
