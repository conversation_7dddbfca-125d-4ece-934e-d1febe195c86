
<!DOCTYPE html>
<html lang="ru" id="app-html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>UniQPaid - Crypto Wallet</title>
    <link rel="stylesheet" href="css/cyberpunk-styles.css">
    <link rel="stylesheet" href="css/dynamic-design.css">
    <!-- <link rel="stylesheet" href="css/font-adjustments.css"> -->
    <!-- Скрипты подключаются в конце body -->
</head>
<body>

    <!-- Clean Background -->
    <div class="cyberpunk-coins-bg"></div>

    <!-- === КРУПНЫЕ ПЛАВАЮЩИЕ КРИПТОИКОНКИ === -->
    <div class="crypto-background">
        <!-- Bitcoin иконка -->
        <div class="floating-crypto crypto-btc-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 12h8M8 8h6a2 2 0 0 1 0 4M8 16h6a2 2 0 0 0 0-4" fill="none" stroke="#ff6b35"/>
                <path d="M12 6v2M12 16v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Ethereum иконка -->
        <div class="floating-crypto crypto-eth-1">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L6 12.5l6 3.5 6-3.5L12 2z" opacity="0.8" fill="#ff6b35"/>
                <path d="M12 16L6 12.5l6 9.5 6-9.5L12 16z" opacity="0.6" fill="#ff6b35"/>
                <path d="M12 2v6l5.5 2.5L12 2z" opacity="0.4" fill="#ff6b35"/>
                <path d="M12 8v8l5.5-3.5L12 8z" opacity="0.3" fill="#ff6b35"/>
            </svg>
        </div>

        <!-- USDT иконка -->
        <div class="floating-crypto crypto-usdt-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 9h8v2H8z" fill="#ff6b35"/>
                <path d="M10 11v4h4v-4" fill="none" stroke="#ff6b35"/>
                <path d="M12 7v2M12 15v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- TON иконка -->
        <div class="floating-crypto crypto-ton-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 8l8 8M16 8l-8 8" stroke="#ff6b35"/>
                <circle cx="12" cy="12" r="3" fill="rgba(255, 107, 53, 0.3)"/>
                <path d="M12 6v2M12 16v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Доллар иконка -->
        <div class="floating-crypto crypto-dollar-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M10 2v20M14 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" fill="none" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Дополнительная Bitcoin иконка -->
        <div class="floating-crypto crypto-btc-2">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="8" fill="none" stroke="#ff6b35"/>
                <path d="M9 12h6M9 9h4a1.5 1.5 0 0 1 0 3M9 15h4a1.5 1.5 0 0 0 0-3" fill="none" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Дополнительная Ethereum иконка -->
        <div class="floating-crypto crypto-eth-2">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L7 13l5 2.5 5-2.5L12 3z" opacity="0.7" fill="#ff6b35"/>
                <path d="M12 17L7 13l5 8 5-8L12 17z" opacity="0.5" fill="#ff6b35"/>
            </svg>
        </div>

        <!-- Дополнительная Доллар иконка -->
        <div class="floating-crypto crypto-dollar-2">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M10 2v20M14 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" fill="none" stroke="#ff6b35"/>
                <circle cx="12" cy="12" r="9" fill="none" stroke="#ff6b35" opacity="0.5"/>
            </svg>
        </div>
    </div>

    <!-- Removed Glitch Lines for Clean Design -->
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <!-- Аватар пока плейсхолдер, можно заменить на img или div с фоном -->
                <div class="user-avatar">
                    <svg class="user-avatar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <use href="./images/elegant-icons.svg#user-elegant"></use>
                    </svg>
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Баланс">
                <span class="balance-amount" id="balance-amount">0</span>
                <span class="balance-currency" data-translate="currency.coins">монет</span>
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section" id="main-content"> <!-- Добавлен класс active-section -->
            <div id="status-message" class="status-message">Ожидание инициализации...</div>
            <h2 id="tasks-title" data-section="tasks" style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                <svg style="width: 28px; height: 28px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 8px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <path d="M9 12l2 2 4-4" fill="none" stroke="#ff6b35"/>
                    <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c1.66 0 3.22.45 4.56 1.23" fill="none" stroke="#ff6b35"/>
                    <path d="M16 3l4 4-4 4" fill="none" stroke="#ff6b35"/>
                </svg>
                <span>Задания</span>
            </h2>
            <button id="openLinkButton" class="action-button purple-button">
                <svg class="button-icon hardcore-icon" data-icon="link" style="color: #ffffff !important; fill: none !important; stroke: #ffffff !important; stroke-width: 2 !important; width: 20px !important; height: 20px !important; display: inline-block !important; visibility: visible !important; opacity: 1 !important; margin-right: 8px !important; pointer-events: none !important;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" fill="none" stroke="#ffffff" stroke-width="2"/>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" fill="none" stroke="#ffffff" stroke-width="2"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Открыть ссылку</span>
                    <span class="ad-counter" id="native-banner-counter"></span>
                </div>
                <div class="reward-badge"></div>
            </button>
            <button id="watchVideoButton" class="action-button blue-button">
                <svg class="button-icon hardcore-icon" data-icon="video" style="color: #ffffff !important; fill: none !important; stroke: #ffffff !important; stroke-width: 2 !important; width: 20px !important; height: 20px !important; display: inline-block !important; visibility: visible !important; opacity: 1 !important; margin-right: 8px !important; pointer-events: none !important;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="23 7 16 12 23 17 23 7" fill="none" stroke="#ffffff" stroke-width="2"/>
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2" fill="none" stroke="#ffffff" stroke-width="2"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Смотреть видео</span>
                    <span class="ad-counter" id="rewarded-video-counter"></span>
                </div>
                <div class="reward-badge"></div>
            </button>
            <button id="openAdButton" class="action-button orange-button">
                <svg class="button-icon hardcore-icon" data-icon="monitor" style="color: #ffffff !important; fill: none !important; stroke: #ffffff !important; stroke-width: 2 !important; width: 20px !important; height: 20px !important; display: inline-block !important; visibility: visible !important; opacity: 1 !important; margin-right: 8px !important; pointer-events: none !important;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" fill="none" stroke="#ffffff" stroke-width="2"/>
                    <line x1="8" y1="21" x2="16" y2="21" stroke="#ffffff" stroke-width="2"/>
                    <line x1="12" y1="17" x2="12" y2="21" stroke="#ffffff" stroke-width="2"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Кликнуть по баннеру</span>
                    <span class="ad-counter" id="interstitial-counter"></span>
                </div>
                <div class="reward-badge"></div>
            </button>

             <!-- <button id="paid-survey-button" class="action-button secondary-action" disabled>
                 <svg class="button-icon"><use href="images/sprite.svg#icon-money"></use></svg>
                 Пройти опрос
            </button> -->
        </main>

        <!-- Секция "Заработок" (Вывод средств - заглушка) -->
        <section class="app-section earn-section page-hidden" id="earn-section"> <!-- Добавлен page-hidden -->
            <h2 id="earnings-title" style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                <svg style="width: 28px; height: 28px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 8px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="6" width="18" height="12" rx="2" fill="none" stroke="#ff6b35"/>
                    <path d="M3 10h18" fill="none" stroke="#ff6b35"/>
                    <circle cx="17" cy="14" r="1" fill="#ff6b35"/>
                    <path d="M7 6V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2" fill="none" stroke="#ff6b35"/>
                </svg>
                <span>Заработок</span>
            </h2>
            <div class="earn-block">
                <h3 id="balance-title" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" fill="none" stroke="#ff6b35"/>
                    </svg>
                    <span>Ваш баланс</span>
                </h3>
                <p>Текущий баланс вашего аккаунта и доступные для вывода средства.</p>
                <div class="current-balance-display" style="text-align: center; padding: 20px; background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 15px; margin: 15px 0;">
                    <span class="balance-amount" id="earn-balance-amount" style="font-size: 28px; color: var(--cyber-accent-neon); font-family: 'Orbitron', monospace; text-shadow: 0 0 10px var(--cyber-glow);">0</span>
                    <span class="balance-currency" style="font-size: 18px; color: var(--cyber-text-primary); margin-left: 8px;" data-translate="currency.coins">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal" style="color: var(--cyber-accent-neon); font-weight: bold;">0</span> <span data-translate="currency.coins">монет</span>.</p>
            </div>

            <!-- Кнопки-табы для переключения разделов -->
            <div class="withdrawal-tabs">
                <button id="calculator-tab" class="withdrawal-tab inactive">
                    <svg style="width: 18px; height: 18px; margin-right: 8px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="4" y="2" width="16" height="20" rx="2"/>
                        <rect x="6" y="4" width="12" height="4" rx="1" fill="currentColor" opacity="0.3"/>
                        <circle cx="8" cy="12" r="1" fill="currentColor"/>
                        <circle cx="12" cy="12" r="1" fill="currentColor"/>
                        <circle cx="16" cy="12" r="1" fill="currentColor"/>
                        <circle cx="8" cy="16" r="1" fill="currentColor"/>
                        <circle cx="12" cy="16" r="1" fill="currentColor"/>
                        <circle cx="16" cy="16" r="1" fill="currentColor"/>
                    </svg>
                    Калькулятор вывода
                </button>
                <button id="withdrawal-tab" class="withdrawal-tab inactive">
                    <svg style="width: 18px; height: 18px; margin-right: 8px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                    Заявка на вывод
                </button>
            </div>

             <div class="earn-block calculator-section hidden" id="calculator-section">
                <h3 id="calculator-title" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <rect x="4" y="2" width="16" height="20" rx="2" fill="none" stroke="#ff6b35"/>
                        <rect x="6" y="4" width="12" height="4" rx="1" fill="#ff6b35" opacity="0.3"/>
                        <circle cx="8" cy="12" r="1" fill="#ff6b35"/>
                        <circle cx="12" cy="12" r="1" fill="#ff6b35"/>
                        <circle cx="16" cy="12" r="1" fill="#ff6b35"/>
                        <circle cx="8" cy="16" r="1" fill="#ff6b35"/>
                        <circle cx="12" cy="16" r="1" fill="#ff6b35"/>
                        <circle cx="16" cy="16" r="1" fill="#ff6b35"/>
                    </svg>
                    <span>Калькулятор вывода</span>
                </h3>
                <p>Рассчитайте сумму для вывода в различных криптовалютах</p>

                <div class="calculator-header">
                    <p class="calculator-subtitle">Курс: 1 монета = $0.001</p>
                    <div class="balance-display">
                        <span class="balance-label">Ваш баланс:</span>
                        <span class="balance-amount" id="calc-balance">0 <span data-translate="currency.coins">монет</span></span>
                    </div>
                </div>

                <!-- Поле ввода суммы -->
                <div class="amount-input-section">
                    <label for="calc-amount" data-translate="earnings.amount_to_withdraw">Сумма для вывода:</label>
                    <div class="input-group">
                        <input type="number" id="calc-amount" placeholder="Введите количество монет" min="0" step="1">
                        <span class="input-suffix" data-translate="currency.coins">монет</span>
                    </div>
                    <div class="amount-info">
                        <span id="dollar-equivalent">= $0.000</span>
                        <span id="balance-check" class="balance-status">Введите сумму</span>
                    </div>
                </div>

                <!-- Табы валют -->
                <div class="currency-tabs-container">
                    <div class="currency-tabs-header">
                        <button class="currency-tab active" data-currency="ton">
                            <svg class="tab-icon" style="color: #ffffff; width: 24px; height: 24px; display: inline-block; visibility: visible; opacity: 1; background: transparent;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <circle cx="12" cy="12" r="10" fill="none" stroke="#ffffff"/>
                                <path d="M8 8l8 8M16 8l-8 8" fill="none" stroke="#ffffff"/>
                                <circle cx="12" cy="12" r="3" fill="rgba(255, 255, 255, 0.3)"/>
                                <path d="M12 6v2M12 16v2" fill="none" stroke="#ffffff"/>
                            </svg>
                            <span class="tab-name">TON</span>
                            <span class="tab-symbol">Telegram</span>
                        </button>
                        <button class="currency-tab" data-currency="eth">
                            <svg class="tab-icon" style="color: #ffffff; width: 24px; height: 24px; display: inline-block; visibility: visible; opacity: 1; background: transparent;" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2L6 12.5l6 3.5 6-3.5L12 2z" opacity="0.8" fill="#ffffff"/>
                                <path d="M12 16L6 12.5l6 9.5 6-9.5L12 16z" opacity="0.6" fill="#ffffff"/>
                                <path d="M12 2v6l5.5 2.5L12 2z" opacity="0.4" fill="#ffffff"/>
                                <path d="M12 8v8l5.5-3.5L12 8z" opacity="0.3" fill="#ffffff"/>
                            </svg>
                            <span class="tab-name">Ethereum</span>
                            <span class="tab-symbol">ETH</span>
                        </button>
                        <button class="currency-tab" data-currency="btc">
                            <svg class="tab-icon" style="color: #ffffff; width: 24px; height: 24px; display: inline-block; visibility: visible; opacity: 1; background: transparent;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <circle cx="12" cy="12" r="10" fill="none" stroke="#ffffff"/>
                                <path d="M8 12h8M8 8h6a2 2 0 0 1 0 4M8 16h6a2 2 0 0 0 0-4" fill="none" stroke="#ffffff"/>
                                <path d="M12 6v2M12 16v2" fill="none" stroke="#ffffff"/>
                            </svg>
                            <span class="tab-name">Bitcoin</span>
                            <span class="tab-symbol">BTC</span>
                        </button>
                        <button class="currency-tab" data-currency="usdttrc20">
                            <svg class="tab-icon" style="color: #ffffff; width: 24px; height: 24px; display: inline-block; visibility: visible; opacity: 1; background: transparent;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <circle cx="12" cy="12" r="10" fill="none" stroke="#ffffff"/>
                                <path d="M8 9h8v2H8z" fill="#ffffff"/>
                                <path d="M10 11v4h4v-4" fill="none" stroke="#ffffff"/>
                                <path d="M12 7v2M12 15v2" fill="none" stroke="#ffffff"/>
                            </svg>
                            <span class="tab-name">USDT</span>
                            <span class="tab-symbol">TRC20</span>
                        </button>
                    </div>

                    <!-- Контент выбранной валюты -->
                    <div class="currency-content">
                        <div class="currency-info-card" id="currency-info">
                            <div class="currency-main-info">
                                <div class="currency-title">
                                    <svg class="currency-icon" id="currency-card-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                        <!-- TON icon by default -->
                                        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor"/>
                                        <path d="M8 8l8 8M16 8l-8 8" fill="none" stroke="currentColor"/>
                                        <circle cx="12" cy="12" r="3" fill="rgba(255, 255, 255, 0.3)"/>
                                        <path d="M12 6v2M12 16v2" fill="none" stroke="currentColor"/>
                                    </svg>
                                    <span class="currency-full-name" id="currency-card-name">TON (Telegram)</span>
                                    <span class="currency-badge status-best">Лучший выбор</span>
                                </div>
                                <div class="currency-requirements">
                                    <!-- Основная информация -->
                                    <div class="requirement-section main-info-section" id="main-info-section">
                                        <div class="requirement-item">
                                            <span class="requirement-label" id="minimum-label" data-translate="earnings.minimum_withdrawal">Минимум к выводу:</span>
                                            <span class="requirement-value">1,000 монет</span>
                                        </div>
                                        <div class="requirement-item">
                                            <span class="requirement-label" id="withdrawal-amount-label" data-translate="earnings.withdrawal_amount">Сумма к выводу:</span>
                                            <span class="requirement-value" id="withdrawal-amount-display">-</span>
                                        </div>
                                        <div class="requirement-item">
                                            <span class="requirement-label" id="network-fee-label" data-translate="earnings.network_fee">Сетевая комиссия:</span>
                                            <span class="requirement-value fee-amount">Загрузка...</span>
                                        </div>
                                    </div>

                                    <!-- Результат расчета -->
                                    <div class="requirement-section result-section">
                                        <div class="requirement-item">
                                            <span class="requirement-label" data-translate="earnings.final_amount">Вы получите:</span>
                                            <span class="requirement-value" id="final-amount-display">-</span>
                                        </div>
                                        <div class="requirement-item">
                                            <span class="requirement-label" data-translate="earnings.efficiency">Эффективность:</span>
                                            <span class="requirement-value" id="efficiency-display">-</span>
                                        </div>
                                    </div>

                                    <!-- Предупреждение о недостаточной сумме -->
                                    <div class="requirement-section warning-section" id="warning-section" style="display: none;">
                                        <div class="requirement-item minimum-info" id="minimum-info-item">
                                            <span class="requirement-label" data-translate="earnings.minimum_withdrawal">Минимум к выводу:</span>
                                            <span class="requirement-value" id="minimum-required-info">-</span>
                                        </div>
                                        <div class="requirement-item missing-info" id="missing-info-item">
                                            <span class="requirement-label" data-translate="earnings.amount_missing">Не хватает:</span>
                                            <span class="requirement-value" id="amount-missing-info">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="action-status-card" id="action-status">
                                <div class="status-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                        <circle cx="12" cy="12" r="5"/>
                                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                                    </svg>
                                </div>
                                <div class="status-text" data-translate="earnings.enter_amount_for_calculation">Введите сумму для расчета</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="earn-block withdrawal-section hidden" id="withdrawal-section">
                <h3 id="withdrawal-title" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M12 2v10" fill="none" stroke="#ff6b35"/>
                        <path d="M8 8l4-4 4 4" fill="none" stroke="#ff6b35"/>
                        <rect x="3" y="14" width="18" height="6" rx="2" fill="none" stroke="#ff6b35"/>
                        <circle cx="7" cy="17" r="1" fill="#ff6b35"/>
                    </svg>
                    <span>Заявка на вывод</span>
                </h3>
                <p data-section="withdrawal_desc">Выберите валюту из калькулятора выше и укажите адрес кошелька для получения средств.</p>

                <!-- Рекомендации к выводу с подсказкой -->
                <div class="withdrawal-recommendations">
                    <h4 class="recommendations-title">
                        <span data-translate="earnings.withdrawal_recommendations">Рекомендации к выводу</span>
                        <button class="help-icon" id="withdrawal-help-btn" title="Показать подсказку">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                                <line x1="12" y1="17" x2="12.01" y2="17"/>
                            </svg>
                            <div class="tooltip" id="withdrawal-tooltip">
                                <p data-translate="earnings.withdrawal_recommendations_tooltip">
                                    Разные биржи и обменные сервисы имеют свои минимальные лимиты для зачисления средств.
                                    Убедитесь, что сумма вывода превышает минимальные требования получающей стороны,
                                    иначе платёж может зависнуть или не дойти до адресата.
                                    Рекомендуем выводить суммы от $5 и выше для надёжного получения средств.
                                </p>
                            </div>
                        </button>
                    </h4>
                </div>

                 <div class="withdrawal-form">
                     <label for="crypto-currency" data-translate="earnings.selected_crypto">Выбранная криптовалюта:</label>
                     <select id="crypto-currency" class="crypto-select">
                         <option value="ton" selected>TON (Telegram)</option>
                         <option value="eth">Ethereum (ETH)</option>
                         <option value="btc">Bitcoin (BTC)</option>
                         <option value="usdttrc20">USDT (TRC20)</option>
                     </select>

                     <label for="withdrawal-amount" data-translate="earnings.amount_to_withdraw_coins">Сумма для вывода (монеты):</label>
                     <input type="number" id="withdrawal-amount" placeholder="Введите сумму" min="0" step="1">

                     <label for="crypto-amount" data-translate="earnings.amount_to_receive">Сумма к получению:</label>
                     <input type="text" id="crypto-amount" class="crypto-amount-field" placeholder="Будет рассчитано автоматически" readonly>

                     <label for="withdrawal-address" data-translate="earnings.wallet_address">Адрес кошелька:</label>
                     <input type="text" id="withdrawal-address" placeholder="Введите адрес кошелька">

                     <button id="request-withdrawal-button" class="action-button primary-action" disabled>
                         Запросить вывод
                     </button>
                 </div>
                 <p class="hint error-message" id="withdrawal-error" style="display: none;"></p>
                 <p class="hint" data-translate="earnings.wallet_warning"><strong>Важно:</strong> Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.</p>
            </div>

             <div class="earn-block">
                <h3 id="history-title" data-section="withdrawal_history" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: var(--accent-primary); filter: drop-shadow(0 0 6px var(--shadow-orange));" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <circle cx="12" cy="12" r="9"/>
                        <path d="M12 7v5l3 3"/>
                        <path d="M3 12a9 9 0 0 1 9-9"/>
                    </svg>
                    <span data-translate="earnings.withdrawal_history_title">История выплат</span>
                </h3>
                <p data-translate="earnings.withdrawal_history_description">Здесь отображается история всех ваших выплат и их статусы.</p>
                <button onclick="forceLoadHistory()" class="refresh-history-btn">
                    <svg class="refresh-icon" style="color: currentColor; fill: none; stroke: currentColor; stroke-width: 1.5; width: 18px; height: 18px; margin-right: 8px; display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" fill="none" stroke="currentColor"/>
                        <path d="M3 3v5h5" fill="none" stroke="currentColor"/>
                        <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" fill="none" stroke="currentColor"/>
                        <path d="M21 21v-5h-5" fill="none" stroke="currentColor"/>
                    </svg>
                    <span data-translate="earnings.refresh_history">Обновить историю</span>
                </button>
                <div id="withdrawal-history" class="withdrawal-history">
                    <div class="history-container">
                        <div id="withdrawal-history-list" class="history-list empty-state">
                            <div class="empty-history-state">
                                <div class="empty-history-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                        <path d="M12 2v10"/>
                                        <path d="M8 8l4-4 4 4"/>
                                        <rect x="3" y="14" width="18" height="6" rx="2"/>
                                        <circle cx="7" cy="17" r="1"/>
                                        <circle cx="17" cy="17" r="1"/>
                                    </svg>
                                </div>
                                <div class="empty-history-content">
                                    <h4 class="empty-history-title">История выплат пуста</h4>
                                    <p class="empty-history-description">
                                        Ваши запросы на вывод будут отображаться здесь
                                    </p>
                                    <div class="empty-history-hint">
                                        <span class="hint-icon">💡</span>
                                        <span>Перейдите в калькулятор, чтобы рассчитать и запросить первый вывод</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Секция "Друзья" -->
        <section class="app-section friends-section page-hidden" id="friends-section"> <!-- Добавлен page-hidden -->
            <h2 id="friends-title" data-section="friends_title" style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                <svg style="width: 28px; height: 28px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; filter: drop-shadow(0 0 8px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" fill="none" stroke="#ff6b35"/>
                    <circle cx="9" cy="7" r="4" fill="none" stroke="#ff6b35"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87" fill="none" stroke="#ff6b35"/>
                    <path d="M16 3.13a4 4 0 010 7.75" fill="none" stroke="#ff6b35"/>
                </svg>
                <span>Друзья и Приглашения</span>
            </h2>
            <div class="friends-block">
                <h3 id="share-title" data-section="share_app" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: none; stroke: #ff6b35; stroke-width: 1.5; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M8 12l4-4 4 4" fill="none" stroke="#ff6b35"/>
                        <path d="M12 16V8" fill="none" stroke="#ff6b35"/>
                        <path d="M3 16v2a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-2" fill="none" stroke="#ff6b35"/>
                    </svg>
                    <span data-translate="friends.share_title">Поделиться</span>
                </h3>
                <p data-translate="friends.share_description">Расскажите друзьям об этом крутом кибер-панк приложении и зарабатывайте вместе!</p>
                <button id="share-app-button" class="action-button purple-button">
                    <svg style="color: currentColor; fill: none; stroke: currentColor; stroke-width: 1.5; width: 18px; height: 18px; margin-right: 8px; display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M8 12l4-4 4 4" fill="none" stroke="currentColor"/>
                        <path d="M12 16V8" fill="none" stroke="currentColor"/>
                        <path d="M3 16v2a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-2" fill="none" stroke="currentColor"/>
                    </svg>
                    Поделиться
                </button>
            </div>
            <div class="friends-block">
                <h3 id="invite-title" data-section="invite_friend" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: none; stroke: #ff6b35; stroke-width: 1.5; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" fill="none" stroke="#ff6b35"/>
                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" fill="none" stroke="#ff6b35"/>
                    </svg>
                    <span data-translate="friends.referral_link_title">Реферальная ссылка</span>
                </h3>
                <p data-translate="friends.referral_link_description">Поделитесь своей уникальной ссылкой и получайте 10% от заработка каждого приглашенного друга!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" value="Генерация ссылки..." readonly>
                    <button id="copy-referral-button" class="copy-button hardcore-icon-button" title="Копировать" disabled>
                        <svg class="copy-icon hardcore-icon" data-icon="copy" style="color: currentColor !important; fill: none !important; stroke: currentColor !important; stroke-width: 2 !important; width: 20px !important; height: 20px !important; display: block !important; margin: 0 auto !important; visibility: visible !important; opacity: 1 !important; filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" fill="none" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="friends-block">
                <h3 id="stats-title" data-section="referral_stats" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: #ff6b35; stroke: #ff6b35; stroke-width: 1.5; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M3 3v18h18" fill="none" stroke="#ff6b35"/>
                        <path d="M7 16l4-4 4 4 6-6" fill="none" stroke="#ff6b35"/>
                        <circle cx="7" cy="16" r="1" fill="#ff6b35"/>
                        <circle cx="11" cy="12" r="1" fill="#ff6b35"/>
                        <circle cx="15" cy="16" r="1" fill="#ff6b35"/>
                        <circle cx="21" cy="10" r="1" fill="#ff6b35"/>
                    </svg>
                    <span>Статистика</span>
                </h3>
                <p data-section="referral_stats_desc">Отслеживайте свои успехи в привлечении новых пользователей.</p>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label" data-translate="friends.total_referrals">Всего рефералов:</div>
                        <div class="stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label" data-translate="friends.earned_from_referrals">Заработано на рефералах:</div>
                        <div class="stat-value" id="referral-earnings">0</div>
                    </div>
                </div>
                <div id="referrals-list" class="referrals-list">
                    <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 20px; text-align: center; color: var(--cyber-text-secondary);">
                        <svg style="width: 24px; height: 24px; margin-right: 8px; color: #FFA500; vertical-align: middle;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                            <path d="M16 3.13a4 4 0 010 7.75"/>
                        </svg>
                        <span data-translate="friends.no_referrals">У вас пока нет рефералов</span><br>
                        <small style="opacity: 0.7;" data-translate="friends.invite_friends_hint">Пригласите друзей и начните зарабатывать!</small>
                    </div>
                </div>
                <button id="refresh-stats-button" class="action-button blue-button">
                    <svg class="refresh-icon" style="color: currentColor; fill: none; stroke: currentColor; stroke-width: 1.5; width: 18px; height: 18px; margin-right: 8px; display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" fill="none" stroke="currentColor"/>
                        <path d="M3 3v5h5" fill="none" stroke="currentColor"/>
                        <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" fill="none" stroke="currentColor"/>
                        <path d="M21 21v-5h-5" fill="none" stroke="currentColor"/>
                    </svg>
                    <span data-translate="buttons.refresh_stats">Обновить статистику</span>
                </button>
            </div>
            <div class="friends-block">
                <h3 id="referrer-title" data-section="referrer_info" style="display: flex; align-items: center; gap: 10px; margin-bottom: 16px;">
                    <svg style="width: 22px; height: 22px; color: #ff6b35; fill: none; stroke: #ff6b35; stroke-width: 1.5; filter: drop-shadow(0 0 6px rgba(255, 107, 53, 0.4)); display: inline-block; visibility: visible; opacity: 1;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                        <circle cx="8.5" cy="7" r="4"/>
                        <path d="M20 8v6"/>
                        <path d="M23 11l-3-3-3 3"/>
                    </svg>
                    <span data-translate="friends.my_referrer_title">Мой реферер</span>
                </h3>
                <p data-translate="friends.referrer_description">Информация о пользователе, который пригласил вас в приложение.</p>
                <div id="referrer-info" class="referrals-list">
                    <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 20px; text-align: center; color: var(--cyber-text-secondary);">
                        <svg style="width: 24px; height: 24px; margin-right: 8px; color: #FFA500; vertical-align: middle;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                            <circle cx="8.5" cy="7" r="4"/>
                            <path d="M20 8v6"/>
                            <path d="M23 11l-3-3-3 3"/>
                        </svg>
                        Загрузка информации о реферере...<br>
                        <small style="opacity: 0.7;">Проверяем, кто вас пригласил</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Нижняя навигация с компактными кибер-иконками -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                <span class="nav-text" data-section="tasks">Главная</span>
            </button>
            <button class="nav-button" id="nav-earn">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="6" width="18" height="12" rx="2"/>
                    <path d="M3 10h18"/>
                    <circle cx="17" cy="14" r="1" fill="currentColor"/>
                    <path d="M7 6V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2"/>
                </svg>
                <span class="nav-text" data-section="earnings">Заработок</span>
            </button>
            <button class="nav-button" id="nav-friends">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                    <path d="M16 3.13a4 4 0 010 7.75"/>
                </svg>
                <span class="nav-text" data-section="referrals">Друзья</span>
            </button>
        </nav>

    </div> <!-- /app-container -->

    <!-- ===== ПОДКЛЮЧЕНИЕ СКРИПТОВ ===== -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>

    <!-- Антифрод система -->
    <script src="js/device-fingerprint.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/fraud-blocker.js"></script>
    <script src="js/fraud-manager.js"></script>

    <!-- Система проверки блокировки -->
    <script src="js/block-checker.js"></script>

    <script src="js/modules-loader.js"></script>

      <!-- Серверная система счетчиков рекламы -->
    <script src="js/server-ad-counters.js"></script>
   
    

</body>
</html>