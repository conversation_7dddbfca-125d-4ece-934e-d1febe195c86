<?php
/**
 * api/check-block-status.php
 * Проверяет статус блокировки пользователя и возвращает соответствующую информацию
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/validate_initdata.php';
require_once __DIR__ . '/functions.php';

try {
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['initData'])) {
        throw new Exception('Отсутствует initData');
    }
    
    // Валидируем initData
    $userData = validateTelegramInitData($input['initData']);
    if (!$userData) {
        throw new Exception('Неверные данные авторизации');
    }
    
    $userId = $userData['id'];
    
    // Загружаем данные пользователей
    $allUserData = loadUserData();
    
    if (!isset($allUserData[$userId])) {
        // Пользователь не найден - не заблокирован
        echo json_encode([
            'success' => true,
            'blocked' => false,
            'user_id' => $userId
        ]);
        exit;
    }
    
    $user = $allUserData[$userId];
    $isBlocked = $user['blocked'] ?? false;
    
    $response = [
        'success' => true,
        'blocked' => $isBlocked,
        'user_id' => $userId
    ];
    
    if ($isBlocked) {
        // Определяем тип блокировки и соответствующую страницу
        $blockReason = $user['block_reason'] ?? 'SECURITY_VIOLATION';
        $blockedAt = $user['blocked_at'] ?? date('Y-m-d H:i:s');
        
        // Определяем тип блокировки
        $blockType = 'general'; // По умолчанию общая блокировка
        $redirectUrl = 'blocked.html';
        
        if (strpos($blockReason, 'VPN') !== false || strpos($blockReason, 'Proxy') !== false) {
            $blockType = 'vpn';
            $redirectUrl = 'vpn-blocked.html';
        } elseif (strpos($blockReason, 'fraud') !== false) {
            $blockType = 'fraud';
            $redirectUrl = 'blocked.html';
        } elseif (strpos($blockReason, 'fingerprint') !== false) {
            $blockType = 'device';
            $redirectUrl = 'blocked.html';
        }
        
        // Добавляем параметры для блокирующей страницы
        $params = [
            'reason' => $blockReason,
            'user_id' => $userId,
            'time' => $blockedAt,
            'type' => $blockType
        ];
        
        // Добавляем специфичные для VPN параметры
        if ($blockType === 'vpn') {
            $params['risk_score'] = $user['vpn_risk_score'] ?? 'High';
            $params['ip'] = $user['blocked_ip'] ?? $user['ip_address'] ?? 'Unknown';
        }
        
        $redirectUrl .= '?' . http_build_query($params);
        
        $response['block_info'] = [
            'reason' => $blockReason,
            'blocked_at' => $blockedAt,
            'block_type' => $blockType,
            'redirect_url' => $redirectUrl,
            'fraud_detected' => $user['fraud_detected'] ?? false,
            'vpn_detected' => $user['vpn_detected'] ?? false
        ];
        
        // Логируем попытку доступа заблокированного пользователя
        error_log("BLOCKED USER ACCESS: User $userId attempted access (Reason: $blockReason)");
        
        // Записываем в лог подозрительной активности
        $logFile = __DIR__ . '/../database/fraud_log.json';
        $fraudLog = [];
        if (file_exists($logFile)) {
            $fraudLog = json_decode(file_get_contents($logFile), true) ?? [];
        }
        
        $fraudLog[] = [
            'timestamp' => time(),
            'user_id' => $userId,
            'activity_type' => 'blocked_user_access_attempt',
            'block_reason' => $blockReason,
            'block_type' => $blockType,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        // Ограничиваем размер лога
        if (count($fraudLog) > 1000) {
            $fraudLog = array_slice($fraudLog, -1000);
        }
        
        file_put_contents($logFile, json_encode($fraudLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log('check-block-status ERROR: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'blocked' => false // В случае ошибки не блокируем доступ
    ]);
}
?>
