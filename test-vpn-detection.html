<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Тест VPN детекции</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            border-left: 4px solid #4299e1;
        }
        
        .result-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }
        
        .risk-score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .risk-low { background: #c6f6d5; color: #22543d; }
        .risk-medium { background: #fef5e7; color: #744210; }
        .risk-high { background: #fed7d7; color: #742a2a; }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .details {
            background: #edf2f7;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Тест VPN детекции</h1>
            <p>Проверьте, обнаруживает ли система ваш VPN/Proxy</p>
        </div>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="testVPNDetection()" id="testBtn">
                🚀 Запустить тест VPN детекции
            </button>
            <button class="test-button" onclick="getMyIP()" id="ipBtn">
                🌐 Узнать мой IP
            </button>
            <button class="test-button" onclick="clearResults()">
                🗑️ Очистить результаты
            </button>
        </div>
        
        <div id="results" class="results" style="display: none;">
            <h3>📊 Результаты анализа</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        async function testVPNDetection() {
            const testBtn = document.getElementById('testBtn');
            const results = document.getElementById('results');
            const resultsContent = document.getElementById('resultsContent');
            
            testBtn.disabled = true;
            testBtn.textContent = '⏳ Анализируем...';
            
            results.style.display = 'block';
            resultsContent.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Выполняется анализ VPN/Proxy...</p>
                </div>
            `;
            
            try {
                // Собираем данные клиента
                const clientData = await collectClientData();
                
                // Отправляем на анализ
                const response = await fetch('api/vpn-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'analyze_vpn',
                        client_data: clientData
                    })
                });
                
                const result = await response.json();
                displayResults(result);
                
            } catch (error) {
                console.error('Ошибка тестирования VPN:', error);
                resultsContent.innerHTML = `
                    <div class="result-item" style="border-left: 4px solid #e53e3e;">
                        <strong>❌ Ошибка:</strong> ${error.message}
                    </div>
                `;
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🚀 Запустить тест VPN детекции';
            }
        }
        
        async function collectClientData() {
            const data = {
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                language: navigator.language,
                platform: navigator.platform,
                userAgent: navigator.userAgent,
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth
                },
                timestamp: Date.now()
            };
            
            // Пытаемся получить геолокацию
            try {
                if (navigator.geolocation) {
                    const position = await new Promise((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(resolve, reject, {
                            timeout: 5000,
                            enableHighAccuracy: false
                        });
                    });
                    
                    data.geolocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy
                    };
                }
            } catch (e) {
                console.log('Геолокация недоступна:', e.message);
            }
            
            return data;
        }
        
        function displayResults(result) {
            const resultsContent = document.getElementById('resultsContent');
            
            if (!result.success) {
                resultsContent.innerHTML = `
                    <div class="result-item" style="border-left: 4px solid #e53e3e;">
                        <strong>❌ Ошибка:</strong> ${result.error || 'Неизвестная ошибка'}
                    </div>
                `;
                return;
            }
            
            const riskScore = result.risk_score || 0;
            const vpnDetected = result.vpn_detected;
            const blocked = result.blocked;
            
            let riskClass = 'risk-low';
            let riskText = 'Низкий риск';
            let riskIcon = '✅';
            
            if (riskScore >= 70) {
                riskClass = 'risk-high';
                riskText = 'Высокий риск';
                riskIcon = '🚨';
            } else if (riskScore >= 40) {
                riskClass = 'risk-medium';
                riskText = 'Средний риск';
                riskIcon = '⚠️';
            }
            
            resultsContent.innerHTML = `
                <div class="risk-score ${riskClass}">
                    ${riskIcon} Риск-скор: ${riskScore}/100<br>
                    <small>${riskText}</small>
                </div>
                
                <div class="result-item">
                    <strong>🔍 VPN/Proxy обнаружен:</strong> 
                    ${vpnDetected ? '❌ ДА' : '✅ НЕТ'}
                </div>
                
                <div class="result-item">
                    <strong>🔒 Статус блокировки:</strong> 
                    ${blocked ? '🚫 ЗАБЛОКИРОВАН' : '✅ Разрешен'}
                </div>
                
                <div class="result-item">
                    <strong>🌐 IP адрес:</strong> ${result.client_ip || 'Неизвестно'}
                </div>
                
                <div class="result-item">
                    <strong>⏰ Время анализа:</strong> ${new Date(result.timestamp * 1000).toLocaleString('ru-RU')}
                </div>
                
                <div class="details">
                    <strong>📋 Подробные данные анализа:</strong><br>
                    ${JSON.stringify(result.analysis, null, 2)}
                </div>
            `;
        }
        
        async function getMyIP() {
            const ipBtn = document.getElementById('ipBtn');
            ipBtn.disabled = true;
            ipBtn.textContent = '⏳ Получаем IP...';
            
            try {
                const response = await fetch('https://api.ipify.org?format=json');
                const data = await response.json();
                
                alert(`🌐 Ваш IP адрес: ${data.ip}`);
            } catch (error) {
                alert('❌ Ошибка получения IP: ' + error.message);
            } finally {
                ipBtn.disabled = false;
                ipBtn.textContent = '🌐 Узнать мой IP';
            }
        }
        
        function clearResults() {
            const results = document.getElementById('results');
            results.style.display = 'none';
        }
    </script>
</body>
</html>
