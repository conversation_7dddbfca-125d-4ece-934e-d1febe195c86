<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест API админки</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Тест API админки антифрод системы</h1>
    
    <div class="test-section">
        <h3>1. Тест загрузки настроек</h3>
        <button onclick="testLoadSettings()">Загрузить настройки</button>
        <div id="load-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Тест сохранения настроек</h3>
        <button onclick="testSaveSettings()">Сохранить настройки</button>
        <div id="save-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Тест загрузки статистики</h3>
        <button onclick="testLoadStats()">Загрузить статистику</button>
        <div id="stats-result"></div>
    </div>

    <script>
        async function testLoadSettings() {
            const resultDiv = document.getElementById('load-result');
            resultDiv.innerHTML = '<p>⏳ Загрузка...</p>';
            
            try {
                const response = await fetch('/api/fraud-detection-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_admin_settings'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Настройки загружены успешно!</h4>
                        <pre>${JSON.stringify(data.settings, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `<h4>❌ Ошибка: ${data.error}</h4>`;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h4>❌ Ошибка запроса: ${error.message}</h4>`;
            }
        }
        
        async function testSaveSettings() {
            const resultDiv = document.getElementById('save-result');
            resultDiv.innerHTML = '<p>⏳ Сохранение...</p>';
            
            const testSettings = {
                enable_antifraud: true,
                fraud_threshold: 60,
                block_vpn: true,
                vpn_threshold: 75,
                block_duplicate_fingerprints: true,
                block_self_referrals: false
            };
            
            try {
                const response = await fetch('/api/fraud-detection-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'save_admin_settings',
                        settings: testSettings
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Настройки сохранены успешно!</h4>
                        <p><strong>Сообщение:</strong> ${data.message}</p>
                        <pre>${JSON.stringify(data.settings, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `<h4>❌ Ошибка: ${data.error}</h4>`;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h4>❌ Ошибка запроса: ${error.message}</h4>`;
            }
        }
        
        async function testLoadStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.innerHTML = '<p>⏳ Загрузка...</p>';
            
            try {
                const response = await fetch('/api/fraud-detection-simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_admin_stats'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Статистика загружена успешно!</h4>
                        <pre>${JSON.stringify(data.stats, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = `<h4>❌ Ошибка: ${data.error}</h4>`;
                }
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h4>❌ Ошибка запроса: ${error.message}</h4>`;
            }
        }
    </script>
</body>
</html>
