<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест локализации рекламных счетчиков</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .warning {
            border-left-color: #ff9800;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
        .counter-test {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .counter-value {
            font-weight: bold;
            color: #4CAF50;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🌐 Тест локализации рекламных счетчиков</h1>
    
    <div class="test-section">
        <h3>📊 Статус системы</h3>
        <div id="systemStatus" class="status">Загрузка...</div>
        <button onclick="checkSystemStatus()">Обновить статус</button>
    </div>
    
    <div class="test-section">
        <h3>🌐 Тест локализации</h3>
        <div id="localizationStatus" class="status">Проверка локализации...</div>
        <button onclick="switchToEnglish()">Switch to English</button>
        <button onclick="switchToRussian()">Переключить на русский</button>
    </div>
    
    <div class="test-section">
        <h3>🔢 Тест форматирования счетчиков</h3>
        <div id="counterTests">
            <div class="counter-test">
                <span>0 показов:</span>
                <span id="counter-0" class="counter-value">-</span>
            </div>
            <div class="counter-test">
                <span>1 показ:</span>
                <span id="counter-1" class="counter-value">-</span>
            </div>
            <div class="counter-test">
                <span>2 показа:</span>
                <span id="counter-2" class="counter-value">-</span>
            </div>
            <div class="counter-test">
                <span>5 показов:</span>
                <span id="counter-5" class="counter-value">-</span>
            </div>
            <div class="counter-test">
                <span>10 показов:</span>
                <span id="counter-10" class="counter-value">-</span>
            </div>
        </div>
        <button onclick="testCounterFormatting()">Тест форматирования</button>
    </div>
    
    <div class="test-section">
        <h3>📝 Лог</h3>
        <div id="consoleLog" class="log"></div>
        <button onclick="clearLog()">Очистить лог</button>
    </div>

    <script>
        // Перехватываем console.log
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };
        
        // Функции тестирования
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            let status = '';
            
            // Проверяем локализацию
            if (window.appLocalization) {
                status += `<strong>AppLocalization:</strong> ${window.appLocalization.isLoaded ? '✅ Загружена' : '❌ Не загружена'}<br>`;
                status += `<strong>Текущий язык:</strong> ${window.appLocalization.currentLanguage || 'Не определен'}<br>`;
            } else {
                status += '<strong>AppLocalization:</strong> ❌ Не найдена<br>';
            }
            
            // Проверяем ServerAdCountersManager
            if (window.serverAdCountersManager) {
                status += `<strong>ServerAdCountersManager:</strong> ${window.serverAdCountersManager.isInitialized ? '✅ Инициализирован' : '❌ Не инициализирован'}<br>`;
                status += `<strong>Локализация в менеджере:</strong> ${window.serverAdCountersManager.localization ? '✅ Есть' : '❌ Нет'}<br>`;
            } else {
                status += '<strong>ServerAdCountersManager:</strong> ❌ Не найден<br>';
            }
            
            statusDiv.innerHTML = status;
        }
        
        function checkLocalizationStatus() {
            const statusDiv = document.getElementById('localizationStatus');
            
            if (window.appLocalization && window.appLocalization.isLoaded) {
                const lang = window.appLocalization.currentLanguage;
                statusDiv.innerHTML = `✅ Локализация работает. Текущий язык: <strong>${lang}</strong>`;
            } else {
                statusDiv.innerHTML = '❌ Локализация не загружена';
            }
        }
        
        function switchToEnglish() {
            if (window.appLocalization) {
                window.appLocalization.setLanguage('en');
                console.log('[TEST] Переключено на английский язык');
                setTimeout(() => {
                    checkLocalizationStatus();
                    testCounterFormatting();
                }, 100);
            } else {
                console.error('[TEST] AppLocalization не найдена');
            }
        }
        
        function switchToRussian() {
            if (window.appLocalization) {
                window.appLocalization.setLanguage('ru');
                console.log('[TEST] Переключено на русский язык');
                setTimeout(() => {
                    checkLocalizationStatus();
                    testCounterFormatting();
                }, 100);
            } else {
                console.error('[TEST] AppLocalization не найдена');
            }
        }
        
        function testCounterFormatting() {
            console.log('[TEST] 🔢 Тестируем форматирование счетчиков...');
            
            if (window.serverAdCountersManager) {
                const testValues = [0, 1, 2, 5, 10];
                
                testValues.forEach(count => {
                    const formatted = window.serverAdCountersManager.formatCounterText(count);
                    const element = document.getElementById(`counter-${count}`);
                    if (element) {
                        element.textContent = formatted;
                    }
                    console.log(`[TEST] ${count} показов = "${formatted}"`);
                });
            } else {
                console.error('[TEST] ServerAdCountersManager не найден');
            }
        }
        
        function clearLog() {
            document.getElementById('consoleLog').textContent = '';
        }
        
        // Автоматическая проверка при загрузке
        window.addEventListener('load', () => {
            console.log('[TEST] 🚀 Страница загружена, начинаем тестирование...');
            setTimeout(() => {
                checkSystemStatus();
                checkLocalizationStatus();
                testCounterFormatting();
            }, 2000);
        });
    </script>
    
    <!-- Подключаем необходимые модули -->
    <script src="js/localization.js"></script>
    <script src="js/server-ad-counters.js"></script>
</body>
</html>
