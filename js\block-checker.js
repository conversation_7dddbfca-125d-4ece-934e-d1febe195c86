/**
 * js/block-checker.js
 * Модуль для проверки статуса блокировки пользователя
 */

class BlockChecker {
    constructor() {
        this.checkInterval = null;
        this.isChecking = false;
        this.lastCheckTime = 0;
        this.checkCooldown = 30000; // 30 секунд между проверками
    }

    /**
     * Инициализация проверки блокировки
     */
    async init() {
        console.log('🔒 BlockChecker: Инициализация проверки блокировки...');
        
        // Проверяем сразу при загрузке
        await this.checkBlockStatus();
        
        // Устанавливаем периодическую проверку каждые 2 минуты
        this.checkInterval = setInterval(() => {
            this.checkBlockStatus();
        }, 120000);
        
        // Проверяем при фокусе на окне
        window.addEventListener('focus', () => {
            this.checkBlockStatus();
        });
        
        // Проверяем при возобновлении работы Telegram WebApp
        if (window.Telegram && window.Telegram.WebApp) {
            window.Telegram.WebApp.onEvent('viewportChanged', () => {
                this.checkBlockStatus();
            });
        }
    }

    /**
     * Проверяет статус блокировки пользователя
     */
    async checkBlockStatus() {
        // Проверяем кулдаун
        const now = Date.now();
        if (now - this.lastCheckTime < this.checkCooldown) {
            return;
        }
        
        if (this.isChecking) {
            return;
        }
        
        this.isChecking = true;
        this.lastCheckTime = now;
        
        try {
            // Получаем initData
            const initData = this.getInitData();
            if (!initData) {
                console.warn('🔒 BlockChecker: Нет initData для проверки');
                return;
            }
            
            console.log('🔒 BlockChecker: Проверяем статус блокировки...');
            
            // Добавляем timestamp для обхода кэша Telegram
            const timestamp = Date.now();
            const response = await fetch(`/api/check-block-status.php?t=${timestamp}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    initData: initData
                })
            });
            
            const result = await response.json();
            
            if (!result.success) {
                console.error('🔒 BlockChecker: Ошибка проверки:', result.error);
                return;
            }
            
            if (result.blocked) {
                console.warn('🚫 BlockChecker: Пользователь заблокирован!', result.block_info);
                this.handleBlockedUser(result.block_info);
            } else {
                console.log('✅ BlockChecker: Пользователь не заблокирован');
            }
            
        } catch (error) {
            console.error('🔒 BlockChecker: Ошибка запроса:', error);
        } finally {
            this.isChecking = false;
        }
    }

    /**
     * Обрабатывает заблокированного пользователя
     */
    handleBlockedUser(blockInfo) {
        // Останавливаем все проверки
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        
        // Показываем уведомление
        this.showBlockNotification(blockInfo);
        
        // Перенаправляем на блокирующую страницу через 3 секунды
        setTimeout(() => {
            this.redirectToBlockPage(blockInfo);
        }, 3000);
    }

    /**
     * Показывает уведомление о блокировке
     */
    showBlockNotification(blockInfo) {
        // Создаем модальное окно с уведомлением
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        `;
        
        const icon = blockInfo.block_type === 'vpn' ? '🛡️' : '🚫';
        const title = blockInfo.block_type === 'vpn' ? 'VPN/Proxy Detected' : 'Access Blocked';
        const message = blockInfo.block_type === 'vpn' 
            ? 'Please disable your VPN/Proxy to continue'
            : 'Your account has been blocked due to security violations';
        
        content.innerHTML = `
            <div style="font-size: 60px; margin-bottom: 20px;">${icon}</div>
            <h2 style="color: #e53e3e; margin-bottom: 15px;">${title}</h2>
            <p style="color: #4a5568; margin-bottom: 20px;">${message}</p>
            <div style="background: #fed7d7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <strong>Reason:</strong> ${blockInfo.reason}
            </div>
            <p style="color: #718096; font-size: 14px;">Redirecting in 3 seconds...</p>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // Анимация появления
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.style.transition = 'opacity 0.3s';
            modal.style.opacity = '1';
        }, 10);
    }

    /**
     * Перенаправляет на блокирующую страницу
     */
    redirectToBlockPage(blockInfo) {
        const redirectUrl = blockInfo.redirect_url || 'blocked.html';
        
        if (window.Telegram && window.Telegram.WebApp) {
            // В Telegram WebApp закрываем приложение
            window.Telegram.WebApp.close();
        } else {
            // В браузере перенаправляем на блокирующую страницу
            window.location.href = redirectUrl;
        }
    }

    /**
     * Получает initData из Telegram WebApp или эмулятора
     */
    getInitData() {
        if (window.Telegram && window.Telegram.WebApp && window.Telegram.WebApp.initData) {
            return window.Telegram.WebApp.initData;
        }
        
        // Для тестирования в браузере
        if (window.telegramEmulator && window.telegramEmulator.initData) {
            return window.telegramEmulator.initData;
        }
        
        return null;
    }

    /**
     * Принудительная проверка блокировки
     */
    async forceCheck() {
        this.lastCheckTime = 0; // Сбрасываем кулдаун
        await this.checkBlockStatus();
    }

    /**
     * Останавливает проверки
     */
    stop() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        this.isChecking = false;
        console.log('🔒 BlockChecker: Проверки остановлены');
    }

    /**
     * Возобновляет проверки
     */
    resume() {
        if (!this.checkInterval) {
            this.init();
            console.log('🔒 BlockChecker: Проверки возобновлены');
        }
    }
}

// Создаем глобальный экземпляр
window.blockChecker = new BlockChecker();

// Автоматическая инициализация при загрузке DOM
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.blockChecker.init();
    });
} else {
    window.blockChecker.init();
}

// Экспортируем для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BlockChecker;
}
