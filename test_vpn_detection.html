<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Тест VPN Детекции</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-safe { background: #2ecc71; }
        .status-warning { background: #f39c12; }
        .status-danger { background: #e74c3c; }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Тест VPN Детекции</h1>
        
        <div class="test-section">
            <h3>📊 Информация о подключении</h3>
            <div class="info-grid">
                <div class="info-card">
                    <strong>Ваш IP:</strong><br>
                    <span id="userIP">Определяется...</span>
                </div>
                <div class="info-card">
                    <strong>User Agent:</strong><br>
                    <span id="userAgent">Определяется...</span>
                </div>
                <div class="info-card">
                    <strong>Заголовки:</strong><br>
                    <span id="headers">Определяются...</span>
                </div>
                <div class="info-card">
                    <strong>Геолокация:</strong><br>
                    <span id="geolocation">Определяется...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 Тесты VPN детекции</h3>
            
            <button class="test-button" onclick="testBasicVPNDetection()">
                🌐 Базовая проверка VPN
            </button>
            
            <button class="test-button" onclick="testAdvancedVPNDetection()">
                🔬 Расширенная проверка VPN
            </button>
            
            <button class="test-button" onclick="testIPReputation()">
                🛡️ Проверка репутации IP
            </button>
            
            <button class="test-button" onclick="testAllVPNServices()">
                🚀 Полная проверка всех сервисов
            </button>
            
            <div class="result-box" id="testResults">
Результаты тестов будут отображены здесь...
            </div>
        </div>
        
        <div class="test-section">
            <h3>📈 Статус VPN детекции</h3>
            <div id="vpnStatus">
                <span class="status-indicator status-warning"></span>
                Ожидание тестирования...
            </div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let currentIP = null;
        let testResults = [];
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Инициализация тестера VPN детекции...');
            loadBasicInfo();
        });
        
        /**
         * Загружает базовую информацию о подключении
         */
        async function loadBasicInfo() {
            try {
                // Получаем IP адрес
                const ipResponse = await fetch('https://api.ipify.org?format=json');
                const ipData = await ipResponse.json();
                currentIP = ipData.ip;
                document.getElementById('userIP').textContent = currentIP;
                
                // User Agent
                document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';
                
                // Заголовки (примерные)
                document.getElementById('headers').innerHTML = `
                    Accept-Language: ${navigator.language}<br>
                    Platform: ${navigator.platform}<br>
                    Cookies: ${navigator.cookieEnabled ? 'Enabled' : 'Disabled'}
                `;
                
                // Геолокация
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            document.getElementById('geolocation').innerHTML = `
                                Lat: ${position.coords.latitude.toFixed(4)}<br>
                                Lng: ${position.coords.longitude.toFixed(4)}
                            `;
                        },
                        () => {
                            document.getElementById('geolocation').textContent = 'Недоступно';
                        }
                    );
                } else {
                    document.getElementById('geolocation').textContent = 'Не поддерживается';
                }
                
            } catch (error) {
                console.error('Ошибка загрузки базовой информации:', error);
                document.getElementById('userIP').textContent = 'Ошибка определения';
            }
        }
        
        /**
         * Базовая проверка VPN
         */
        async function testBasicVPNDetection() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.textContent = '🔍 Выполняется базовая проверка VPN...\n';

            try {
                // Собираем максимум данных для анализа
                const clientData = {
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    screen: `${screen.width}x${screen.height}`,
                    language: navigator.language,
                    languages: navigator.languages,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    doNotTrack: navigator.doNotTrack,
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    maxTouchPoints: navigator.maxTouchPoints,
                    userAgent: navigator.userAgent,
                    webdriver: navigator.webdriver,
                    connection: navigator.connection ? {
                        effectiveType: navigator.connection.effectiveType,
                        downlink: navigator.connection.downlink,
                        rtt: navigator.connection.rtt
                    } : null
                };

                const response = await fetch('api/vpn-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'analyze_vpn',
                        ip_address: currentIP,
                        user_agent: navigator.userAgent,
                        client_data: clientData,
                        headers: {
                            'Accept-Language': navigator.language,
                            'Platform': navigator.platform
                        }
                    })
                });

                const result = await response.json();

                resultDiv.textContent += `\n📊 Результат базовой проверки:\n`;
                resultDiv.textContent += `Status: ${response.status}\n`;
                resultDiv.textContent += `VPN обнаружен: ${result.vpn_detected ? 'ДА ⚠️' : 'НЕТ ✅'}\n`;
                resultDiv.textContent += `Риск-скор: ${result.risk_score || 0}/100\n`;
                resultDiv.textContent += `IP адрес: ${result.client_ip}\n`;

                // Показываем детальный анализ
                if (result.analysis) {
                    resultDiv.textContent += `\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ:\n`;

                    if (result.analysis.ip_analysis) {
                        resultDiv.textContent += `📍 IP Анализ:\n`;
                        resultDiv.textContent += `  - Приватный IP: ${result.analysis.ip_analysis.is_private ? 'Да' : 'Нет'}\n`;
                        resultDiv.textContent += `  - Зарезервированный IP: ${result.analysis.ip_analysis.is_reserved ? 'Да' : 'Нет'}\n`;
                        resultDiv.textContent += `  - Reverse DNS: ${result.analysis.ip_analysis.reverse_dns || 'Не найден'}\n`;
                        resultDiv.textContent += `  - Подозрительный hostname: ${result.analysis.ip_analysis.suspicious_hostname ? 'Да' : 'Нет'}\n`;
                    }

                    if (result.analysis.header_analysis) {
                        resultDiv.textContent += `\n📋 Анализ заголовков:\n`;
                        resultDiv.textContent += `  - Прокси заголовки: ${Object.keys(result.analysis.header_analysis.proxy_headers || {}).length}\n`;
                        resultDiv.textContent += `  - Подозрительный User Agent: ${result.analysis.header_analysis.suspicious_user_agent ? 'Да' : 'Нет'}\n`;
                        if (result.analysis.header_analysis.proxy_headers && Object.keys(result.analysis.header_analysis.proxy_headers).length > 0) {
                            resultDiv.textContent += `  - Найденные прокси заголовки: ${JSON.stringify(result.analysis.header_analysis.proxy_headers, null, 4)}\n`;
                        }
                    }
                }

                resultDiv.textContent += `\n📄 Полный результат: ${JSON.stringify(result, null, 2)}\n`;

                updateVPNStatus(result.vpn_detected, result.risk_score);

            } catch (error) {
                resultDiv.textContent += `\n❌ Ошибка: ${error.message}\n`;
                console.error('Ошибка базовой проверки VPN:', error);
            }
        }
        
        /**
         * Расширенная проверка VPN
         */
        async function testAdvancedVPNDetection() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.textContent = '🔬 Выполняется расширенная проверка VPN...\n';
            
            try {
                // Собираем дополнительные данные
                const fingerprint = {
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    screen: `${screen.width}x${screen.height}`,
                    language: navigator.language,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    doNotTrack: navigator.doNotTrack
                };
                
                const response = await fetch('api/vpn-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'analyze_vpn',
                        ip_address: currentIP,
                        user_agent: navigator.userAgent,
                        fingerprint: fingerprint,
                        advanced: true
                    })
                });
                
                const result = await response.json();
                
                resultDiv.textContent += `\n📊 Результат расширенной проверки:\n`;
                resultDiv.textContent += `Status: ${response.status}\n`;
                resultDiv.textContent += `VPN обнаружен: ${result.vpn_detected ? 'ДА' : 'НЕТ'}\n`;
                resultDiv.textContent += `Риск-скор: ${result.risk_score || 0}\n`;
                resultDiv.textContent += `Отпечаток: ${JSON.stringify(fingerprint, null, 2)}\n`;
                resultDiv.textContent += `Полный результат: ${JSON.stringify(result, null, 2)}\n`;
                
                updateVPNStatus(result.vpn_detected, result.risk_score);
                
            } catch (error) {
                resultDiv.textContent += `\n❌ Ошибка: ${error.message}\n`;
                console.error('Ошибка расширенной проверки VPN:', error);
            }
        }
        
        /**
         * Проверка репутации IP
         */
        async function testIPReputation() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.textContent = '🛡️ Проверяется репутация IP...\n';
            
            try {
                const response = await fetch('api/vpn-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'check_ip_reputation',
                        ip_address: currentIP
                    })
                });
                
                const result = await response.json();
                
                resultDiv.textContent += `\n📊 Результат проверки репутации:\n`;
                resultDiv.textContent += `Status: ${response.status}\n`;
                resultDiv.textContent += `Результат: ${JSON.stringify(result, null, 2)}\n`;
                
            } catch (error) {
                resultDiv.textContent += `\n❌ Ошибка: ${error.message}\n`;
                console.error('Ошибка проверки репутации IP:', error);
            }
        }
        
        /**
         * Полная проверка всех сервисов
         */
        async function testAllVPNServices() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.textContent = '🚀 Выполняется полная проверка всех сервисов...\n';
            
            // Запускаем все тесты последовательно
            await testBasicVPNDetection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAdvancedVPNDetection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testIPReputation();
            
            resultDiv.textContent += `\n✅ Все тесты завершены!\n`;
        }
        
        /**
         * Обновляет статус VPN детекции
         */
        function updateVPNStatus(vpnDetected, riskScore) {
            const statusDiv = document.getElementById('vpnStatus');
            
            if (vpnDetected) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-danger"></span>
                    VPN ОБНАРУЖЕН! Риск-скор: ${riskScore}
                `;
            } else if (riskScore > 50) {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-warning"></span>
                    Подозрительная активность. Риск-скор: ${riskScore}
                `;
            } else {
                statusDiv.innerHTML = `
                    <span class="status-indicator status-safe"></span>
                    Соединение безопасно. Риск-скор: ${riskScore}
                `;
            }
        }
    </script>
</body>
</html>
