<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой тест Fraud API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .warning {
            border-left-color: #ff9800;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error-result {
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>🧪 Простой тест Fraud Detection API</h1>
    
    <div class="test-section">
        <h3>📡 Тест подключения к API</h3>
        <button onclick="testConnection()">Тест GET запроса</button>
        <button onclick="testBasicPost()">Тест POST запроса</button>
        <button onclick="testFraudCheck()">Тест проверки фрода</button>
        <div id="connectionResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>🔍 Детальная диагностика</h3>
        <button onclick="testWithDetails()">Полная диагностика</button>
        <div id="detailsResult" class="result"></div>
    </div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.textContent = 'Тестируем GET запрос...';
            
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                console.log('Response text:', text);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        resultDiv.textContent = `✅ GET запрос успешен:\n${JSON.stringify(data, null, 2)}`;
                        resultDiv.className = 'result success';
                    } catch (e) {
                        resultDiv.textContent = `⚠️ Ответ получен, но не JSON:\n${text}`;
                        resultDiv.className = 'result warning';
                    }
                } else {
                    resultDiv.textContent = `❌ HTTP ошибка ${response.status}:\n${text}`;
                    resultDiv.className = 'result error-result';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Ошибка сети: ${error.message}`;
                resultDiv.className = 'result error-result';
            }
        }
        
        async function testBasicPost() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.textContent = 'Тестируем базовый POST запрос...';
            
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test_connection'
                    })
                });
                
                const text = await response.text();
                console.log('Response text:', text);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        resultDiv.textContent = `✅ POST запрос успешен:\n${JSON.stringify(data, null, 2)}`;
                        resultDiv.className = 'result success';
                    } catch (e) {
                        resultDiv.textContent = `⚠️ Ответ получен, но не JSON:\n${text}`;
                        resultDiv.className = 'result warning';
                    }
                } else {
                    resultDiv.textContent = `❌ HTTP ошибка ${response.status}:\n${text}`;
                    resultDiv.className = 'result error-result';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Ошибка сети: ${error.message}`;
                resultDiv.className = 'result error-result';
            }
        }
        
        async function testFraudCheck() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.textContent = 'Тестируем проверку фрода...';
            
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'check_fraud',
                        user_id: '12345',
                        device_fingerprint: {
                            canvas: 'test_canvas_hash',
                            webgl: 'test_webgl_hash',
                            screen: '1920x1080',
                            timezone: 'Europe/Moscow'
                        },
                        ip_address: '127.0.0.1'
                    })
                });
                
                const text = await response.text();
                console.log('Response text:', text);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        resultDiv.textContent = `✅ Проверка фрода успешна:\n${JSON.stringify(data, null, 2)}`;
                        resultDiv.className = 'result success';
                    } catch (e) {
                        resultDiv.textContent = `⚠️ Ответ получен, но не JSON:\n${text}`;
                        resultDiv.className = 'result warning';
                    }
                } else {
                    resultDiv.textContent = `❌ HTTP ошибка ${response.status}:\n${text}`;
                    resultDiv.className = 'result error-result';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Ошибка сети: ${error.message}`;
                resultDiv.className = 'result error-result';
            }
        }
        
        async function testWithDetails() {
            const resultDiv = document.getElementById('detailsResult');
            resultDiv.textContent = 'Запускаем полную диагностику...\n';
            
            // Тест 1: GET запрос
            resultDiv.textContent += '\n1. Тестируем GET запрос...\n';
            try {
                const response = await fetch('api/fraud-detection.php');
                const text = await response.text();
                resultDiv.textContent += `   Status: ${response.status}\n`;
                resultDiv.textContent += `   Response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}\n`;
            } catch (e) {
                resultDiv.textContent += `   Ошибка: ${e.message}\n`;
            }
            
            // Тест 2: POST без данных
            resultDiv.textContent += '\n2. Тестируем POST без данных...\n';
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: ''
                });
                const text = await response.text();
                resultDiv.textContent += `   Status: ${response.status}\n`;
                resultDiv.textContent += `   Response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}\n`;
            } catch (e) {
                resultDiv.textContent += `   Ошибка: ${e.message}\n`;
            }
            
            // Тест 3: POST с неверными данными
            resultDiv.textContent += '\n3. Тестируем POST с неверными данными...\n';
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: '{"invalid": "data"}'
                });
                const text = await response.text();
                resultDiv.textContent += `   Status: ${response.status}\n`;
                resultDiv.textContent += `   Response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}\n`;
            } catch (e) {
                resultDiv.textContent += `   Ошибка: ${e.message}\n`;
            }
            
            // Тест 4: Правильный POST
            resultDiv.textContent += '\n4. Тестируем правильный POST...\n';
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'test_fraud_status',
                        user_id: '12345'
                    })
                });
                const text = await response.text();
                resultDiv.textContent += `   Status: ${response.status}\n`;
                resultDiv.textContent += `   Response: ${text.substring(0, 300)}${text.length > 300 ? '...' : ''}\n`;
            } catch (e) {
                resultDiv.textContent += `   Ошибка: ${e.message}\n`;
            }
            
            resultDiv.textContent += '\n✅ Диагностика завершена';
        }
    </script>
</body>
</html>
