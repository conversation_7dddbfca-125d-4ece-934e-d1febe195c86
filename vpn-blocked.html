<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN/Proxy Detected</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .vpn-blocked-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            padding: 40px;
            max-width: 550px;
            width: 90%;
            text-align: center;
            animation: bounceIn 0.6s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .vpn-icon {
            font-size: 100px;
            margin-bottom: 20px;
            animation: shake 1s infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .vpn-title {
            font-size: 32px;
            font-weight: bold;
            color: #e53e3e;
            margin-bottom: 15px;
        }
        
        .vpn-subtitle {
            font-size: 18px;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .warning-message {
            background: #fed7d7;
            border: 2px solid #fc8181;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            color: #742a2a;
            line-height: 1.6;
        }
        
        .warning-message h3 {
            color: #c53030;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .divider {
            height: 3px;
            background: linear-gradient(90deg, transparent, #fc8181, transparent);
            margin: 25px 0;
            border-radius: 2px;
        }
        
        .warning-message-ru {
            background: #e6fffa;
            border: 2px solid #4fd1c7;
            border-radius: 15px;
            padding: 25px;
            color: #234e52;
            line-height: 1.6;
        }
        
        .warning-message-ru h3 {
            color: #2c7a7b;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .vpn-detection-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            font-family: monospace;
            font-size: 14px;
            color: #4a5568;
        }
        
        .instructions {
            background: #f0fff4;
            border: 2px solid #68d391;
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            text-align: left;
        }
        
        .instructions h4 {
            color: #22543d;
            margin-bottom: 15px;
            text-align: center;
            font-size: 18px;
        }
        
        .instructions ol {
            color: #2f855a;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .retry-button {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 25px;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }
        
        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }
        
        .timestamp {
            margin-top: 25px;
            font-size: 12px;
            color: #718096;
            font-style: italic;
        }
        
        .alert-badge {
            display: inline-block;
            background: #fed7d7;
            color: #742a2a;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
            border: 2px solid #fc8181;
        }
        
        .risk-score {
            background: #1a202c;
            color: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="vpn-blocked-container">
        <div class="vpn-icon">🛡️</div>
        
        <div class="alert-badge">🚨 VPN/PROXY DETECTED</div>
        
        <h1 class="vpn-title">VPN/Proxy Detected</h1>
        <p class="vpn-subtitle">Please disable your VPN or proxy to continue</p>
        
        <div class="warning-message">
            <h3>⚠️ Access Restricted</h3>
            <p><strong>Our security system has detected that you are using a VPN or proxy service.</strong></p>
            <p>For security reasons and to prevent fraud, access through VPN/proxy connections is not allowed.</p>
            <p>Please disable your VPN/proxy and try again with your regular internet connection.</p>
        </div>
        
        <div class="divider"></div>
        
        <div class="warning-message-ru">
            <h3>⚠️ Доступ ограничен</h3>
            <p><strong>Наша система безопасности обнаружила, что вы используете VPN или прокси-сервис.</strong></p>
            <p>По соображениям безопасности и для предотвращения мошенничества доступ через VPN/прокси соединения не разрешен.</p>
            <p>Пожалуйста, отключите VPN/прокси и попробуйте снова с обычным интернет-соединением.</p>
        </div>
        
        <div class="vpn-detection-info">
            <strong>Detection Details:</strong><br>
            Risk Score: <span id="riskScore">High</span><br>
            Detection Time: <span id="detectionTime"></span><br>
            Your IP: <span id="userIP">Hidden</span><br>
            Block ID: <span id="blockId"></span>
        </div>
        
        <div class="instructions">
            <h4>🔧 How to disable VPN/Proxy:</h4>
            <ol>
                <li><strong>Mobile Apps:</strong> Close VPN apps (NordVPN, ExpressVPN, etc.)</li>
                <li><strong>Browser Extensions:</strong> Disable VPN browser extensions</li>
                <li><strong>System VPN:</strong> Turn off VPN in device settings</li>
                <li><strong>Proxy Settings:</strong> Disable proxy in browser/system settings</li>
                <li><strong>Wait 1-2 minutes</strong> after disabling VPN</li>
                <li><strong>Refresh this page</strong> or restart the app</li>
            </ol>
        </div>
        
        <button class="retry-button" onclick="retryAccess()">
            🔄 I've Disabled VPN - Retry Access
        </button>
        
        <div class="timestamp">
            Detected at: <span id="blockTime"></span>
        </div>
    </div>

    <script>
        // Получаем параметры из URL
        const urlParams = new URLSearchParams(window.location.search);
        const riskScore = urlParams.get('risk_score') || 'High';
        const userId = urlParams.get('user_id') || 'Unknown';
        const userIP = urlParams.get('ip') || 'Hidden';
        const blockTime = urlParams.get('time') || new Date().toISOString();
        
        // Обновляем информацию на странице
        document.getElementById('riskScore').textContent = riskScore;
        document.getElementById('detectionTime').textContent = new Date().toLocaleString();
        document.getElementById('userIP').textContent = userIP;
        document.getElementById('blockId').textContent = 'VPN-' + Date.now().toString().slice(-6);
        document.getElementById('blockTime').textContent = new Date(blockTime).toLocaleString();
        
        // Функция повторной попытки доступа
        function retryAccess() {
            // Показываем индикатор загрузки
            const button = document.querySelector('.retry-button');
            const originalText = button.textContent;
            button.textContent = '⏳ Checking connection...';
            button.disabled = true;
            
            // Ждем 2 секунды и перенаправляем
            setTimeout(() => {
                if (window.Telegram && window.Telegram.WebApp) {
                    window.Telegram.WebApp.close();
                } else {
                    window.location.href = '/';
                }
            }, 2000);
        }
        
        // Логируем попытку доступа через VPN
        fetch('/api/fraud-detection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'log_suspicious_activity',
                activity_type: 'vpn_access_attempt',
                details: {
                    user_id: userId,
                    risk_score: riskScore,
                    ip_address: userIP,
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent,
                    referrer: document.referrer
                }
            })
        }).catch(e => console.log('VPN logging failed:', e));
        
        // Автоматическая проверка каждые 30 секунд
        let checkInterval = setInterval(() => {
            fetch('/api/vpn-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'analyze_vpn',
                    client_data: {
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        timestamp: Date.now()
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && !data.vpn_detected) {
                    // VPN больше не обнаружен
                    clearInterval(checkInterval);
                    alert('✅ VPN больше не обнаружен! Перенаправляем...');
                    retryAccess();
                }
            })
            .catch(e => console.log('Auto-check failed:', e));
        }, 30000);
    </script>
</body>
</html>
