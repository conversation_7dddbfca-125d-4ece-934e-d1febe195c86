<?php
/**
 * api/fraud-detection-simple.php
 * Упрощенная версия API для тестирования
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            echo json_encode([
                'status' => 'ok',
                'message' => 'Fraud Detection API работает',
                'timestamp' => time()
            ]);
            exit;
        }
        throw new Exception('Отсутствует параметр action');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'test_connection':
            echo json_encode([
                'success' => true, 
                'message' => 'API работает', 
                'timestamp' => time()
            ]);
            break;

        case 'get_admin_settings':
            // Загружаем настройки из файла
            $settingsFile = __DIR__ . '/../database/antifraud_settings.json';
            $defaultSettings = [
                'enable_antifraud' => true,
                'fraud_threshold' => 50,
                'block_vpn' => true,
                'vpn_threshold' => 70,
                'block_duplicate_fingerprints' => true,
                'block_self_referrals' => true,
                'updated_at' => time()
            ];

            if (file_exists($settingsFile)) {
                $settings = json_decode(file_get_contents($settingsFile), true);
                if ($settings) {
                    $settings = array_merge($defaultSettings, $settings);
                } else {
                    $settings = $defaultSettings;
                }
            } else {
                $settings = $defaultSettings;
            }

            echo json_encode([
                'success' => true,
                'settings' => $settings
            ]);
            break;

        case 'save_admin_settings':
            // Эмулируем сохранение настроек
            $settingsFile = __DIR__ . '/../database/antifraud_settings.json';
            
            if (!is_dir(dirname($settingsFile))) {
                mkdir(dirname($settingsFile), 0755, true);
            }
            
            $settings = $input['settings'] ?? [];
            $validatedSettings = [
                'enable_antifraud' => (bool)($settings['enable_antifraud'] ?? true),
                'fraud_threshold' => max(10, min(100, (int)($settings['fraud_threshold'] ?? 50))),
                'block_vpn' => (bool)($settings['block_vpn'] ?? true),
                'vpn_threshold' => max(30, min(100, (int)($settings['vpn_threshold'] ?? 70))),
                'block_duplicate_fingerprints' => (bool)($settings['block_duplicate_fingerprints'] ?? true),
                'block_self_referrals' => (bool)($settings['block_self_referrals'] ?? true),
                'updated_at' => time()
            ];
            
            $result = file_put_contents($settingsFile, json_encode($validatedSettings, JSON_PRETTY_PRINT));

            if ($result !== false) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Настройки антифрод системы успешно сохранены!',
                    'settings' => $validatedSettings
                ]);
            } else {
                throw new Exception('Не удалось сохранить настройки в файл');
            }
            break;

        case 'get_admin_stats':
            // Возвращаем базовую статистику
            echo json_encode([
                'success' => true,
                'stats' => [
                    'total_fingerprints' => 0,
                    'blocked_devices' => 0,
                    'duplicate_fingerprints' => 0,
                    'fraud_attempts' => 0,
                    'vpn_detections' => 0,
                    'vpn_blocked' => 0
                ],
                'fraud_log' => [],
                'blocked_devices' => []
            ]);
            break;

        case 'clear_fraud_log':
            echo json_encode([
                'success' => true,
                'message' => 'Журнал очищен'
            ]);
            break;

        case 'unblock_device':
            echo json_encode([
                'success' => true,
                'message' => 'Устройство разблокировано'
            ]);
            break;

        case 'get_all_fingerprints':
            echo json_encode([
                'success' => true,
                'fingerprints' => [],
                'total_count' => 0
            ]);
            break;

        case 'get_fingerprint_details':
            echo json_encode([
                'success' => true,
                'details' => [
                    'fingerprint' => $input['fingerprint'] ?? 'unknown',
                    'user_id' => $input['user_id'] ?? 'unknown',
                    'components' => null,
                    'ip_address' => null,
                    'timestamp' => null,
                    'risk_score' => 0,
                    'blocked' => false
                ]
            ]);
            break;

        case 'block_device':
            echo json_encode([
                'success' => true,
                'message' => 'Устройство заблокировано',
                'blocked_users' => 0
            ]);
            break;

        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log('fraud-detection-simple ERROR: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
