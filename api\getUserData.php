<?php
/**
 * МИНИМАЛЬНАЯ ВЕРСИЯ getUserData.php для тестирования
 */

header('Content-Type: application/json');

try {
    // Проверяем метод
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Только POST']);
        exit;
    }

    // Читаем данные
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Нет initData']);
        exit;
    }

    // Извлекаем user ID из initData
    parse_str($input['initData'], $parts);
    $user = json_decode($parts['user'] ?? '{}', true);

    if (!$user || !isset($user['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Нет ID пользователя']);
        exit;
    }

    $userId = intval($user['id']);

    // Возвращаем минимальный ответ
    echo json_encode([
        'userId' => $userId,
        'balance' => 0,
        'min_withdrawal' => 0,
        'min_balance_for_withdrawal' => 100,
        'withdrawals_count' => 0,
        'test' => true
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
