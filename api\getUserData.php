<?php
/**
 * api/getUserData.php
 * API для получения данных пользователя (исправленная версия)
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Функция для безопасного логирования
function safeLog($message, $isError = false) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] getUserData: $message\n";
    @error_log($logMessage, 3, __DIR__ . '/getUserData.log');
    
    if ($isError) {
        error_log("getUserData ERROR: $message");
    }
}

// Функция для возврата ошибки
function returnError($message, $httpCode = 500, $details = null) {
    safeLog($message, true);
    http_response_code($httpCode);
    
    $response = [
        'error' => $message,
        'timestamp' => time(),
        'debug' => true
    ];
    
    if ($details) {
        $response['details'] = $details;
    }
    
    echo json_encode($response);
    exit;
}

try {
    safeLog("=== Начало обработки запроса getUserData ===");
    
    // Проверяем метод запроса
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        returnError('Метод должен быть POST', 405);
    }
    
    // Проверяем наличие необходимых файлов
    $requiredFiles = [
        'config.php' => __DIR__ . '/config.php',
        'validate_initdata.php' => __DIR__ . '/validate_initdata.php',
        'db_mock.php' => __DIR__ . '/db_mock.php'
    ];
    
    $missingFiles = [];
    foreach ($requiredFiles as $name => $path) {
        if (!file_exists($path)) {
            $missingFiles[] = $name;
        }
    }
    
    if (!empty($missingFiles)) {
        returnError('Отсутствуют файлы: ' . implode(', ', $missingFiles), 500, $missingFiles);
    }
    
    safeLog("Все необходимые файлы найдены");
    
    // Подключаем файлы
    require_once $requiredFiles['config.php'];
    require_once $requiredFiles['validate_initdata.php'];
    require_once $requiredFiles['db_mock.php'];
    
    safeLog("Файлы подключены успешно");
    
    // Проверяем наличие функций
    $requiredFunctions = ['validateTelegramInitData', 'loadUserData', 'saveUserData'];
    $missingFunctions = [];
    
    foreach ($requiredFunctions as $func) {
        if (!function_exists($func)) {
            $missingFunctions[] = $func;
        }
    }
    
    if (!empty($missingFunctions)) {
        returnError('Отсутствуют функции: ' . implode(', ', $missingFunctions), 500, $missingFunctions);
    }
    
    safeLog("Все необходимые функции найдены");
    
    // Получаем входные данные
    $inputJSON = file_get_contents('php://input');
    if ($inputJSON === false) {
        returnError('Не удалось прочитать входные данные', 400);
    }
    
    $input = json_decode($inputJSON, true);
    if ($input === null) {
        returnError('Некорректный JSON в запросе', 400, ['json_error' => json_last_error_msg()]);
    }
    
    if (!isset($input['initData']) || empty($input['initData'])) {
        returnError('Отсутствует initData в запросе', 400);
    }
    
    $initData = $input['initData'];
    safeLog("Получен initData длиной: " . strlen($initData));
    
    // Валидируем initData
    $validatedData = validateTelegramInitData($initData);
    
    if ($validatedData === false) {
        safeLog("Стандартная валидация не прошла, пробуем упрощенную");
        
        // Упрощенная валидация
        $initDataParts = [];
        parse_str($initData, $initDataParts);
        
        if (isset($initDataParts['user'])) {
            $userArray = json_decode($initDataParts['user'], true);
            if ($userArray !== null && isset($userArray['id'])) {
                $validatedData = ['user' => $userArray];
                safeLog("Упрощенная валидация прошла для пользователя: " . $userArray['id']);
            } else {
                returnError('Не удалось извлечь данные пользователя из initData', 403);
            }
        } else {
            returnError('Отсутствуют данные пользователя в initData', 403);
        }
    } else {
        safeLog("Стандартная валидация прошла успешно");
    }
    
    // Получаем ID пользователя
    $userId = (string)$validatedData['user']['id'];
    safeLog("ID пользователя: $userId");
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        returnError('loadUserData вернул некорректные данные', 500);
    }
    
    safeLog("Данные пользователей загружены, всего: " . count($userData));
    
    // Получаем или создаем пользователя
    if (!isset($userData[$userId])) {
        // Создаем нового пользователя
        $userData[$userId] = [
            'id' => $userId,
            'balance' => 0,
            'withdrawals_count' => 0,
            'created_at' => time(),
            'last_activity' => time(),
            'username' => $validatedData['user']['username'] ?? null,
            'first_name' => $validatedData['user']['first_name'] ?? null,
            'last_name' => $validatedData['user']['last_name'] ?? null,
            'blocked' => false
        ];
        
        safeLog("Создан новый пользователь: $userId");
    } else {
        // Обновляем активность существующего пользователя
        $userData[$userId]['last_activity'] = time();
        
        // Обновляем данные профиля
        if (isset($validatedData['user']['username'])) {
            $userData[$userId]['username'] = $validatedData['user']['username'];
        }
        if (isset($validatedData['user']['first_name'])) {
            $userData[$userId]['first_name'] = $validatedData['user']['first_name'];
        }
        if (isset($validatedData['user']['last_name'])) {
            $userData[$userId]['last_name'] = $validatedData['user']['last_name'];
        }
        
        safeLog("Обновлен существующий пользователь: $userId");
    }
    
    $userDetails = $userData[$userId];
    
    // Проверяем блокировку
    if (isset($userDetails['blocked']) && $userDetails['blocked']) {
        returnError('Ваш аккаунт заблокирован из-за подозрительной активности', 403);
    }
    
    // Сохраняем данные
    if (!saveUserData($userData)) {
        returnError('Не удалось сохранить данные пользователя', 500);
    }
    
    safeLog("Данные пользователя сохранены: $userId");
    
    // Формируем успешный ответ
    $response = [
        'userId' => $userId,
        'balance' => (int)$userDetails['balance'],
        'min_withdrawal' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0,
        'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100,
        'withdrawals_count' => (int)($userDetails['withdrawals_count'] ?? 0),
        'username' => $userDetails['username'] ?? null,
        'first_name' => $userDetails['first_name'] ?? null,
        'success' => true,
        'timestamp' => time()
    ];
    
    safeLog("Успешный ответ для пользователя: $userId, баланс: " . $userDetails['balance']);
    
    http_response_code(200);
    echo json_encode($response);
    
} catch (Exception $e) {
    returnError('Исключение: ' . $e->getMessage(), 500, [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
} catch (Error $e) {
    returnError('Фатальная ошибка: ' . $e->getMessage(), 500, [
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
