<?php
/**
 * api/getUserData-fixed.php
 * Исправленная версия API для получения данных пользователя
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Функция для логирования с выводом в JSON при ошибках
function logAndRespond($message, $isError = false, $httpCode = 500) {
    error_log("getUserData: $message");
    if ($isError) {
        http_response_code($httpCode);
        echo json_encode([
            'error' => $message,
            'debug' => true,
            'timestamp' => time()
        ]);
        exit;
    }
}

try {
    // Проверяем наличие файлов
    $requiredFiles = [
        __DIR__ . '/config.php',
        __DIR__ . '/validate_initdata.php', 
        __DIR__ . '/db_mock.php'
    ];
    
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            logAndRespond("Отсутствует файл: " . basename($file), true, 500);
        }
    }
    
    // Подключаем файлы
    require_once __DIR__ . '/config.php';
    require_once __DIR__ . '/validate_initdata.php';
    require_once __DIR__ . '/db_mock.php';
    
    // Проверяем наличие функций
    if (!function_exists('validateTelegramInitData')) {
        logAndRespond("Функция validateTelegramInitData не найдена", true, 500);
    }
    
    if (!function_exists('loadUserData')) {
        logAndRespond("Функция loadUserData не найдена", true, 500);
    }
    
    if (!function_exists('saveUserData')) {
        logAndRespond("Функция saveUserData не найдена", true, 500);
    }
    
    // Получаем входные данные
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);
    
    if ($input === null) {
        logAndRespond("Некорректный JSON в запросе", true, 400);
    }
    
    if (!isset($input['initData']) || empty($input['initData'])) {
        logAndRespond("Отсутствует initData в запросе", true, 400);
    }
    
    $initData = $input['initData'];
    logAndRespond("Получен initData длиной: " . strlen($initData));
    
    // Валидируем initData
    $validatedData = validateTelegramInitData($initData);
    
    if ($validatedData === false) {
        // Пытаемся упрощенную валидацию
        logAndRespond("Стандартная валидация не прошла, пробуем упрощенную");
        
        $initDataParts = [];
        parse_str($initData, $initDataParts);
        
        if (isset($initDataParts['user'])) {
            $userArray = json_decode($initDataParts['user'], true);
            if ($userArray !== null && isset($userArray['id'])) {
                $validatedData = ['user' => $userArray];
                logAndRespond("Упрощенная валидация прошла для пользователя: " . $userArray['id']);
            } else {
                logAndRespond("Не удалось извлечь данные пользователя из initData", true, 403);
            }
        } else {
            logAndRespond("Отсутствуют данные пользователя в initData", true, 403);
        }
    }
    
    // Получаем ID пользователя
    $userId = (string)$validatedData['user']['id']; // Оставляем как строку
    logAndRespond("ID пользователя: $userId");
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        logAndRespond("loadUserData вернул не массив", true, 500);
    }
    
    logAndRespond("Данные пользователей загружены, всего пользователей: " . count($userData));
    
    // Получаем или создаем пользователя
    if (!isset($userData[$userId])) {
        // Создаем нового пользователя
        $userData[$userId] = [
            'id' => $userId,
            'balance' => 0,
            'withdrawals_count' => 0,
            'created_at' => time(),
            'last_activity' => time(),
            'username' => $validatedData['user']['username'] ?? null,
            'first_name' => $validatedData['user']['first_name'] ?? null,
            'last_name' => $validatedData['user']['last_name'] ?? null,
            'blocked' => false
        ];
        
        logAndRespond("Создан новый пользователь: $userId");
    } else {
        // Обновляем активность существующего пользователя
        $userData[$userId]['last_activity'] = time();
        
        // Обновляем данные профиля если они изменились
        if (isset($validatedData['user']['username'])) {
            $userData[$userId]['username'] = $validatedData['user']['username'];
        }
        if (isset($validatedData['user']['first_name'])) {
            $userData[$userId]['first_name'] = $validatedData['user']['first_name'];
        }
        if (isset($validatedData['user']['last_name'])) {
            $userData[$userId]['last_name'] = $validatedData['user']['last_name'];
        }
        
        logAndRespond("Обновлен существующий пользователь: $userId");
    }
    
    $userDetails = $userData[$userId];
    
    // Проверяем блокировку
    if (isset($userDetails['blocked']) && $userDetails['blocked']) {
        logAndRespond("Пользователь заблокирован: $userId", true, 403);
    }
    
    // Сохраняем данные
    if (!saveUserData($userData)) {
        logAndRespond("Не удалось сохранить данные пользователя", true, 500);
    }
    
    logAndRespond("Данные пользователя сохранены: $userId");
    
    // Возвращаем успешный ответ
    $response = [
        'userId' => $userId,
        'balance' => (int)$userDetails['balance'],
        'min_withdrawal' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0,
        'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100,
        'withdrawals_count' => (int)($userDetails['withdrawals_count'] ?? 0),
        'username' => $userDetails['username'] ?? null,
        'first_name' => $userDetails['first_name'] ?? null,
        'success' => true
    ];
    
    logAndRespond("Успешный ответ для пользователя: $userId");
    
    http_response_code(200);
    echo json_encode($response);
    
} catch (Exception $e) {
    logAndRespond("Исключение: " . $e->getMessage(), true, 500);
} catch (Error $e) {
    logAndRespond("Фатальная ошибка: " . $e->getMessage(), true, 500);
}
?>
