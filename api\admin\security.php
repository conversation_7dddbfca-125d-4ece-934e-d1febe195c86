<?php
/**
 * api/admin/security.php
 * Страница безопасности
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить security.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $userId = intval($_POST['user_id']);
    
    switch ($_POST['action']) {
        case 'unblock_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = false;
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно разблокирован";
                    
                    // Логируем разблокировку пользователя
                    logAuditEvent('admin_unblock_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'block_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = true;
                $userData[$userId]['blocked_at'] = time();
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно заблокирован";
                    
                    // Логируем блокировку пользователя
                    logAuditEvent('admin_block_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'reset_suspicious':
            if (isset($userData[$userId])) {
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Счетчик подозрительной активности пользователя $userId успешно сброшен";
                    
                    // Логируем сброс счетчика подозрительной активности
                    logAuditEvent('admin_reset_suspicious', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
    }
}

// Получение журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_slice(array_reverse(file($auditLogFile)), 0, 100);
}

// Фильтрация пользователей с подозрительной активностью
$suspiciousUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers[$userId] = $user;
    }
}

// Сортировка пользователей с подозрительной активностью по убыванию счетчика
uasort($suspiciousUsers, function($a, $b) {
    return ($b['suspicious_activity'] ?? 0) - ($a['suspicious_activity'] ?? 0);
});

// Фильтрация заблокированных пользователей
$blockedUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers[$userId] = $user;
    }
}

// Сортировка заблокированных пользователей по дате блокировки
uasort($blockedUsers, function($a, $b) {
    return ($b['blocked_at'] ?? 0) - ($a['blocked_at'] ?? 0);
});

// Обработка POST запроса для переключения лимитов
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_limits'])) {
    $settingsFile = __DIR__ . '/ad_limits_settings.json';

    try {
        // Загружаем текущие настройки
        if (!file_exists($settingsFile)) {
            $settings = [
                'disable_all_ad_limits' => false,
                'last_updated' => null,
                'updated_by' => null
            ];
        } else {
            $content = file_get_contents($settingsFile);
            $settings = json_decode($content, true);
            if (!$settings) {
                $settings = [
                    'disable_all_ad_limits' => false,
                    'last_updated' => null,
                    'updated_by' => null
                ];
            }
        }

        // Переключаем состояние
        $oldValue = $settings['disable_all_ad_limits'];
        $newValue = !$oldValue;

        $settings['disable_all_ad_limits'] = $newValue;
        $settings['last_updated'] = date('Y-m-d H:i:s');
        $settings['updated_by'] = $_SESSION['admin_username'] ?? 'admin';

        // Сохраняем настройки
        $result = file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result !== false) {
            $action = $newValue ? 'ОТКЛЮЧЕНЫ' : 'ВКЛЮЧЕНЫ';
            error_log("AD_LIMITS_CONTROL: Лимиты рекламы {$action} администратором через POST");

            $successMessage = $newValue ? 'Все лимиты рекламы отключены!' : 'Лимиты рекламы включены!';
        } else {
            $errorMessage = 'Ошибка сохранения настроек';
        }

    } catch (Exception $e) {
        error_log("security.php: Ошибка переключения лимитов: " . $e->getMessage());
        $errorMessage = 'Ошибка: ' . $e->getMessage();
    }
}

// Получаем текущие настройки лимитов
$limitsSettings = null;
$settingsFile = __DIR__ . '/ad_limits_settings.json';
try {
    if (file_exists($settingsFile)) {
        $content = file_get_contents($settingsFile);
        $limitsSettings = json_decode($content, true);
    }
    if (!$limitsSettings) {
        $limitsSettings = ['disable_all_ad_limits' => false];
    }
} catch (Exception $e) {
    error_log("security.php: Ошибка загрузки настроек лимитов: " . $e->getMessage());
    $limitsSettings = ['disable_all_ad_limits' => false];
}

// Получаем данные о лимитах для встраивания в страницу
try {
    $limitsData = getCurrentLimits();
    $limitsJson = json_encode($limitsData, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("security.php: Ошибка получения лимитов: " . $e->getMessage());
    $limitsData = null;
    $limitsJson = 'null';
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<!-- Chart.js для графиков мониторинга -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<style>
.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e3e6f0;
}
.log-entry:last-child {
    border-bottom: none;
}
#peak-hours-list .badge {
    margin: 0.1rem;
}
.table th {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
}
.spinner-border {
    width: 2rem;
    height: 2rem;
}
</style>

<div class="container-fluid">
    <div class="row">        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Безопасность</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Вкладки -->
            <ul class="nav nav-tabs mb-4" id="securityTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="suspicious-tab" data-bs-toggle="tab" data-bs-target="#suspicious" type="button" role="tab" aria-controls="suspicious" aria-selected="true">
                        Подозрительная активность <span class="badge bg-warning"><?php echo count($suspiciousUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="blocked-tab" data-bs-toggle="tab" data-bs-target="#blocked" type="button" role="tab" aria-controls="blocked" aria-selected="false">
                        Заблокированные пользователи <span class="badge bg-danger"><?php echo count($blockedUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" type="button" role="tab" aria-controls="audit" aria-selected="false">
                        Журнал аудита
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="limits-tab" data-bs-toggle="tab" data-bs-target="#limits" type="button" role="tab" aria-controls="limits" aria-selected="false">
                        Лимиты системы <span class="badge bg-info" id="peak-time-badge"></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="antifraud-tab" data-bs-toggle="tab" data-bs-target="#antifraud" type="button" role="tab" aria-controls="antifraud" aria-selected="false">
                        🛡️ Антифрод система <span class="badge bg-success" id="antifraud-status-badge">Активна</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="fingerprints-tab" data-bs-toggle="tab" data-bs-target="#fingerprints" type="button" role="tab" aria-controls="fingerprints" aria-selected="false">
                        🔍 Отпечатки устройств <span class="badge bg-info" id="fingerprints-count-badge">0</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab" aria-controls="monitoring" aria-selected="false">
                        📊 Мониторинг <span class="badge bg-warning" id="threats-count-badge">0</span>
                    </button>
                </li>
            </ul>

            <!-- Содержимое вкладок -->
            <div class="tab-content" id="securityTabsContent">
                <!-- Подозрительная активность -->
                <div class="tab-pane fade show active" id="suspicious" role="tabpanel" aria-labelledby="suspicious-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Пользователи с подозрительной активностью</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($suspiciousUsers)): ?>
                                <p class="text-center">Нет пользователей с подозрительной активностью</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Уровень подозрительности</th>
                                                <th>Баланс</th>
                                                <th>Дата регистрации</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($suspiciousUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo min(100, ($user['suspicious_activity'] / SUSPICIOUS_ACTIVITY_THRESHOLD) * 100); ?>%" aria-valuenow="<?php echo $user['suspicious_activity']; ?>" aria-valuemin="0" aria-valuemax="<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>">
                                                                <?php echo $user['suspicious_activity']; ?>/<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?></td>
                                                    <td>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="block_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger">
                                                                <i class="bi bi-lock"></i> Заблокировать
                                                            </button>
                                                        </form>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="reset_suspicious">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-warning">
                                                                <i class="bi bi-arrow-counterclockwise"></i> Сбросить
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Заблокированные пользователи -->
                <div class="tab-pane fade" id="blocked" role="tabpanel" aria-labelledby="blocked-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Заблокированные пользователи</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($blockedUsers)): ?>
                                <p class="text-center">Нет заблокированных пользователей</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Дата блокировки</th>
                                                <th>Баланс</th>
                                                <th>Подозрительная активность</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($blockedUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td><?php echo isset($user['blocked_at']) ? date('Y-m-d H:i', $user['blocked_at']) : 'Неизвестно'; ?></td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo $user['suspicious_activity'] ?? 0; ?></td>
                                                    <td>
                                                        <form method="post">
                                                            <input type="hidden" name="action" value="unblock_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success">
                                                                <i class="bi bi-unlock"></i> Разблокировать
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Журнал аудита -->
                <div class="tab-pane fade" id="audit" role="tabpanel" aria-labelledby="audit-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Журнал аудита (последние 100 записей)</h6>
                            <form method="post">
                                <input type="hidden" name="action" value="clear_audit_log">
                                <button type="submit" class="btn btn-sm btn-outline-danger">Очистить журнал</button>
                            </form>
                        </div>
                        <div class="card-body">
                            <?php if (empty($auditLog)): ?>
                                <p class="text-center">Журнал аудита пуст</p>
                            <?php else: ?>
                                <div style="max-height: 500px; overflow-y: auto;">
                                    <?php foreach ($auditLog as $logEntry): ?>
                                        <div class="log-entry small"><?php echo htmlspecialchars($logEntry); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Лимиты системы -->
                <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Текущие лимиты системы безопасности</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary me-2" onclick="testSession()">
                                    <i class="fas fa-bug"></i> Тест сессии
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshLimits()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Сообщения об успехе/ошибке -->
                            <?php if (isset($successMessage)): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($successMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($errorMessage)): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($errorMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <!-- Управление лимитами -->
                            <div class="alert alert-warning mb-4" id="limits-control-section">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="alert-heading mb-2">
                                            <i class="fas fa-exclamation-triangle"></i> Управление лимитами рекламы
                                        </h5>
                                        <p class="mb-0">
                                            <strong>Внимание:</strong> Отключение лимитов полностью снимает ограничения на просмотры рекламы.
                                            IP-ограничения и блокировка подозрительных пользователей остаются активными.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <form method="POST" style="display: inline;" id="limitsToggleForm">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="disableAllLimits"
                                                       <?php echo $limitsSettings['disable_all_ad_limits'] ? 'checked' : ''; ?>
                                                       onchange="if(confirmToggleLimits(this)) this.form.submit();">
                                                <input type="hidden" name="toggle_limits" value="1">
                                                <label class="form-check-label fw-bold" for="disableAllLimits">
                                                    Отключить все лимиты рекламы
                                                </label>
                                            </div>
                                        </form>
                                        <small class="<?php echo $limitsSettings['disable_all_ad_limits'] ? 'text-danger fw-bold' : 'text-success'; ?>">
                                            <?php echo $limitsSettings['disable_all_ad_limits'] ? 'Все лимиты рекламы отключены' : 'Лимиты рекламы активны'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div id="limits-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка информации о лимитах...</p>
                            </div>

                            <div id="limits-content" style="display: none;">
                                <!-- Статус системы -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card border-left-primary">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                            Статус системы
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="system-status">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-left-info">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                            Время UTC
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="current-time">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Лимиты рекламы по типам -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Лимиты просмотров рекламы по типам</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Просмотров в час (по типу)</strong></td>
                                                        <td><span id="base-type-hour">-</span></td>
                                                        <td><span id="current-type-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Просмотров в день (по типу)</strong></td>
                                                        <td><span id="base-type-day">-</span></td>
                                                        <td><span id="current-type-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-day" class="badge">-</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Общие лимиты -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Общие лимиты системы</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Общих просмотров в час</strong></td>
                                                        <td><span id="base-general-hour">-</span></td>
                                                        <td><span id="current-general-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Общих просмотров в день</strong></td>
                                                        <td><span id="base-general-day">-</span></td>
                                                        <td><span id="current-general-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-day" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Запросов в минуту (IP)</strong></td>
                                                        <td><span id="base-requests-minute">-</span></td>
                                                        <td><span id="current-requests-minute" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Порог подозрительной активности</strong></td>
                                                        <td><span id="base-suspicious">-</span></td>
                                                        <td><span id="current-suspicious" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Пиковые часы -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Настройки пиковых часов</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Пиковые часы (UTC):</strong></p>
                                                <div id="peak-hours-list" class="mb-3">
                                                    Загрузка...
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Множители в пиковое время:</strong></p>
                                                <ul>
                                                    <li>Часовые лимиты: <span class="badge bg-warning">x2.0</span></li>
                                                    <li>Дневные лимиты: <span class="badge bg-info">x1.5</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Антифрод система -->
                <div class="tab-pane fade" id="antifraud" role="tabpanel" aria-labelledby="antifraud-tab">
                    <div class="row">
                        <!-- Статистика антифрод -->
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">📊 Статистика антифрод системы</h6>
                                </div>
                                <div class="card-body">
                                    <div id="antifraud-stats-loading" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Загрузка...</span>
                                        </div>
                                        <p class="mt-2">Загрузка статистики...</p>
                                    </div>
                                    <div id="antifraud-stats-content" style="display: none;">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-success" id="total-fingerprints">0</h4>
                                                    <small class="text-muted">Всего отпечатков</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-danger" id="blocked-devices">0</h4>
                                                    <small class="text-muted">Заблокировано устройств</small>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-warning" id="duplicate-fingerprints">0</h4>
                                                    <small class="text-muted">Дубликаты отпечатков</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-info" id="fraud-attempts">0</h4>
                                                    <small class="text-muted">Попытки фрода</small>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-info" id="vpn-detections">0</h4>
                                                    <small class="text-muted">🌐 VPN детекций</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-danger" id="vpn-blocked">0</h4>
                                                    <small class="text-muted">🚫 VPN заблокировано</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Настройки антифрод -->
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">⚙️ Настройки антифрод системы</h6>
                                </div>
                                <div class="card-body">
                                    <form id="antifraud-settings-form">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enable-antifraud" checked>
                                                <label class="form-check-label" for="enable-antifraud">
                                                    Включить антифрод систему
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="fraud-threshold" class="form-label">Порог фрода (баллы)</label>
                                            <input type="number" class="form-control" id="fraud-threshold" value="50" min="10" max="100">
                                            <div class="form-text">При превышении этого порога пользователь будет заблокирован</div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-vpn" checked>
                                                <label class="form-check-label" for="block-vpn">
                                                    Блокировать VPN/Proxy
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="vpn-threshold" class="form-label">Порог VPN детекции</label>
                                            <input type="number" class="form-control" id="vpn-threshold" min="30" max="100" value="70">
                                            <div class="form-text">Риск-скор для автоматической блокировки VPN (30-100)</div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-duplicate-fingerprints" checked>
                                                <label class="form-check-label" for="block-duplicate-fingerprints">
                                                    Блокировать дубликаты отпечатков
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-self-referrals" checked>
                                                <label class="form-check-label" for="block-self-referrals">
                                                    Блокировать самореферралы
                                                </label>
                                            </div>
                                        </div>

                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Сохранить настройки
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Журнал фрода -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">🚨 Журнал фрода (последние 50 записей)</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshFraudLog()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="clearFraudLog()">
                                    <i class="fas fa-trash"></i> Очистить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="fraud-log-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка журнала фрода...</p>
                            </div>
                            <div id="fraud-log-content" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Время</th>
                                                <th>Пользователь</th>
                                                <th>Тип нарушения</th>
                                                <th>Риск</th>
                                                <th>Баллы</th>
                                                <th>Действие</th>
                                                <th>Детали</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fraud-log-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Управление заблокированными устройствами -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">🔒 Заблокированные устройства</h6>
                        </div>
                        <div class="card-body">
                            <div id="blocked-devices-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка заблокированных устройств...</p>
                            </div>
                            <div id="blocked-devices-content" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Отпечаток устройства</th>
                                                <th>Пользователи</th>
                                                <th>Дата блокировки</th>
                                                <th>Причина</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody id="blocked-devices-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Отпечатки устройств -->
                <div class="tab-pane fade" id="fingerprints" role="tabpanel" aria-labelledby="fingerprints-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">🔍 Отпечатки устройств пользователей</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshFingerprints()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="exportFingerprints()">
                                    <i class="fas fa-download"></i> Экспорт
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="fingerprints-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка отпечатков устройств...</p>
                            </div>
                            <div id="fingerprints-content" style="display: none;">
                                <!-- Фильтры -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="fingerprint-search" placeholder="Поиск по User ID или отпечатку...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-control" id="fingerprint-filter">
                                            <option value="">Все пользователи</option>
                                            <option value="blocked">Заблокированные</option>
                                            <option value="suspicious">Подозрительные</option>
                                            <option value="duplicates">С дубликатами</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-control" id="fingerprint-sort">
                                            <option value="date_desc">По дате (новые)</option>
                                            <option value="date_asc">По дате (старые)</option>
                                            <option value="user_id">По User ID</option>
                                            <option value="risk_score">По риск-скору</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-primary w-100" onclick="applyFingerprintFilters()">
                                            <i class="fas fa-filter"></i> Фильтр
                                        </button>
                                    </div>
                                </div>

                                <!-- Таблица отпечатков -->
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>User ID</th>
                                                <th>Отпечаток устройства</th>
                                                <th>Дата регистрации</th>
                                                <th>IP адрес</th>
                                                <th>Риск-скор</th>
                                                <th>Статус</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fingerprints-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Пагинация -->
                                <nav aria-label="Fingerprints pagination">
                                    <ul class="pagination justify-content-center" id="fingerprints-pagination">
                                        <!-- Пагинация генерируется через JavaScript -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- Модальное окно для просмотра деталей отпечатка -->
                    <div class="modal fade" id="fingerprintModal" tabindex="-1" aria-labelledby="fingerprintModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="fingerprintModalLabel">🔍 Детали отпечатка устройства</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body" id="fingerprintModalBody">
                                    <!-- Содержимое загружается через JavaScript -->
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                                    <button type="button" class="btn btn-danger" id="blockFingerprintBtn" onclick="blockFingerprint()">
                                        <i class="fas fa-ban"></i> Заблокировать устройство
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Мониторинг и статистика -->
                <div class="tab-pane fade" id="monitoring" role="tabpanel" aria-labelledby="monitoring-tab">
                    <div class="row">
                        <!-- Статистика в реальном времени -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow">
                                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                    <h6 class="m-0 font-weight-bold text-primary">📊 Статистика в реальном времени</h6>
                                    <div>
                                        <select class="form-select form-select-sm" id="stats-period" onchange="updateStatsPeriod()">
                                            <option value="1h">Последний час</option>
                                            <option value="6h">Последние 6 часов</option>
                                            <option value="24h" selected>Последние 24 часа</option>
                                            <option value="7d">Последние 7 дней</option>
                                            <option value="30d">Последние 30 дней</option>
                                        </select>
                                        <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshMonitoring()">
                                            <i class="fas fa-sync-alt"></i> Обновить
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="real-time-stats">
                                        <div class="col-md-3 mb-3">
                                            <div class="card border-left-primary h-100">
                                                <div class="card-body">
                                                    <div class="row no-gutters align-items-center">
                                                        <div class="col mr-2">
                                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Попытки фрода</div>
                                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="fraud-attempts">0</div>
                                                        </div>
                                                        <div class="col-auto">
                                                            <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="card border-left-warning h-100">
                                                <div class="card-body">
                                                    <div class="row no-gutters align-items-center">
                                                        <div class="col mr-2">
                                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">VPN детекции</div>
                                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="vpn-detections">0</div>
                                                        </div>
                                                        <div class="col-auto">
                                                            <i class="fas fa-user-secret fa-2x text-gray-300"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="card border-left-danger h-100">
                                                <div class="card-body">
                                                    <div class="row no-gutters align-items-center">
                                                        <div class="col mr-2">
                                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Заблокировано</div>
                                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="blocked-users">0</div>
                                                        </div>
                                                        <div class="col-auto">
                                                            <i class="fas fa-ban fa-2x text-gray-300"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <div class="card border-left-success h-100">
                                                <div class="card-body">
                                                    <div class="row no-gutters align-items-center">
                                                        <div class="col mr-2">
                                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Эффективность</div>
                                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="success-rate">0%</div>
                                                        </div>
                                                        <div class="col-auto">
                                                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Графики -->
                        <div class="col-md-8 mb-4">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">📈 Активность угроз по времени</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="threats-timeline-chart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">🎯 Типы угроз</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="threat-types-chart" width="300" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Топ угроз -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">🚨 Топ угроз за период</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>User ID</th>
                                                    <th>Причина блокировки</th>
                                                    <th>Риск-скор</th>
                                                    <th>IP адрес</th>
                                                    <th>Отпечаток устройства</th>
                                                    <th>Время блокировки</th>
                                                </tr>
                                            </thead>
                                            <tbody id="top-threats-table">
                                                <tr>
                                                    <td colspan="6" class="text-center">Загрузка данных...</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Статус системы -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">⚡ Статус системы</h6>
                                </div>
                                <div class="card-body">
                                    <div id="system-status" class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> Загрузка статуса системы...
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Активные угрозы (последний час):</strong>
                                            <span id="active-threats" class="badge bg-warning">0</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Последняя активность:</strong>
                                            <span id="last-activity">Неизвестно</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Обновлено:</strong>
                                            <span id="last-update">Никогда</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Встроенные данные о лимитах
const embeddedLimitsData = <?php echo $limitsJson; ?>;

// Встроенные настройки лимитов
const embeddedLimitsSettings = <?php echo json_encode($limitsSettings, JSON_UNESCAPED_UNICODE); ?>;

// Функция для загрузки настроек лимитов (теперь использует встроенные данные)
function loadLimitsSettings() {
    console.log('Используем встроенные настройки лимитов:', embeddedLimitsSettings);

    const checkbox = document.getElementById('disableAllLimits');
    const statusText = document.getElementById('limits-status-text');

    if (checkbox && embeddedLimitsSettings) {
        checkbox.checked = embeddedLimitsSettings.disable_all_ad_limits;
    }

    if (statusText && embeddedLimitsSettings) {
        if (embeddedLimitsSettings.disable_all_ad_limits) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }
}

// Функция для загрузки информации о лимитах
function loadLimitsInfo() {
    document.getElementById('limits-loading').style.display = 'block';
    document.getElementById('limits-content').style.display = 'none';

    // Сначала пробуем использовать встроенные данные
    if (embeddedLimitsData) {
        console.log('Используем встроенные данные о лимитах');
        const mockResponse = {
            success: true,
            timestamp: Math.floor(Date.now() / 1000),
            current_time_utc: new Date().toISOString().slice(0, 19).replace('T', ' '),
            current_time_local: new Date().toLocaleString(),
            limits_info: embeddedLimitsData,
            status: embeddedLimitsData.is_peak_time ? 'PEAK_TIME' : 'NORMAL_TIME',
            multipliers: {
                peak_hourly_multiplier: 2.0,
                peak_daily_multiplier: 1.5
            }
        };

        updateLimitsDisplay(mockResponse);
        document.getElementById('limits-loading').style.display = 'none';
        document.getElementById('limits-content').style.display = 'block';
        return;
    }

    // Если встроенных данных нет, делаем AJAX запрос
    fetch('current_limits.php', {
        method: 'GET',
        credentials: 'same-origin', // Включаем cookies для сессии
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Сессия истекла. Пожалуйста, войдите в систему заново.');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateLimitsDisplay(data);
                document.getElementById('limits-loading').style.display = 'none';
                document.getElementById('limits-content').style.display = 'block';
            } else {
                throw new Error(data.error || 'Ошибка загрузки данных');
            }
        })
        .catch(error => {
            console.error('Ошибка загрузки лимитов:', error);
            document.getElementById('limits-loading').innerHTML =
                '<div class="alert alert-danger"><strong>Ошибка загрузки данных о лимитах:</strong><br>' + error.message +
                '<br><button class="btn btn-sm btn-primary mt-2" onclick="loadLimitsInfo()">Попробовать снова</button>' +
                '<br><button class="btn btn-sm btn-secondary mt-2" onclick="testSession()">Тест сессии</button></div>';
        });
}

// Функция для обновления отображения лимитов
function updateLimitsDisplay(data) {
    const limits = data.limits_info.limits;
    const baseLimits = data.limits_info.base_limits;
    const isPeakTime = data.limits_info.is_peak_time;

    // Используем встроенные настройки для определения состояния лимитов
    const limitsDisabled = embeddedLimitsSettings ? embeddedLimitsSettings.disable_all_ad_limits : false;

    // Обновляем переключатель лимитов (но не меняем его состояние, так как это управляется формой)
    const statusText = document.getElementById('limits-status-text');

    if (statusText) {
        if (limitsDisabled) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }

    // Обновляем статус системы
    if (limitsDisabled) {
        document.getElementById('system-status').textContent = 'ЛИМИТЫ ОТКЛЮЧЕНЫ';
        document.getElementById('system-status').className = 'h5 mb-0 font-weight-bold text-danger';
    } else {
        document.getElementById('system-status').textContent =
            isPeakTime ? 'ПИКОВОЕ ВРЕМЯ' : 'ОБЫЧНОЕ ВРЕМЯ';
        document.getElementById('system-status').className =
            'h5 mb-0 font-weight-bold ' + (isPeakTime ? 'text-warning' : 'text-success');
    }

    // Обновляем время
    document.getElementById('current-time').textContent = data.current_time_utc;

    // Обновляем badge в заголовке вкладки
    const badge = document.getElementById('peak-time-badge');
    if (limitsDisabled) {
        badge.textContent = 'ОТКЛЮЧЕНЫ';
        badge.className = 'badge bg-danger';
    } else if (isPeakTime) {
        badge.textContent = 'ПИКОВОЕ';
        badge.className = 'badge bg-warning';
    } else {
        badge.textContent = 'ОБЫЧНОЕ';
        badge.className = 'badge bg-success';
    }

    // Лимиты по типам рекламы
    document.getElementById('base-type-hour').textContent = baseLimits.ad_views_per_type_per_hour;
    document.getElementById('current-type-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_hour;
    updateMultiplierBadge('multiplier-type-hour', baseLimits.ad_views_per_type_per_hour, limits.ad_views_per_type_per_hour, limitsDisabled);

    document.getElementById('base-type-day').textContent = baseLimits.ad_views_per_type_per_day;
    document.getElementById('current-type-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_day;
    updateMultiplierBadge('multiplier-type-day', baseLimits.ad_views_per_type_per_day, limits.ad_views_per_type_per_day, limitsDisabled);

    // Общие лимиты
    document.getElementById('base-general-hour').textContent = baseLimits.ad_views_per_hour;
    document.getElementById('current-general-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_hour;
    updateMultiplierBadge('multiplier-general-hour', baseLimits.ad_views_per_hour, limits.ad_views_per_hour, limitsDisabled);

    document.getElementById('base-general-day').textContent = baseLimits.ad_views_per_day;
    document.getElementById('current-general-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_day;
    updateMultiplierBadge('multiplier-general-day', baseLimits.ad_views_per_day, limits.ad_views_per_day, limitsDisabled);

    // Статические лимиты
    document.getElementById('base-requests-minute').textContent = limits.requests_per_minute;
    document.getElementById('current-requests-minute').textContent = limits.requests_per_minute;

    document.getElementById('base-suspicious').textContent = limits.suspicious_activity_threshold;
    document.getElementById('current-suspicious').textContent = limits.suspicious_activity_threshold;

    // Пиковые часы
    const peakHours = data.limits_info.peak_hours.map(h => h + ':00').join(', ');
    document.getElementById('peak-hours-list').innerHTML =
        '<span class="badge bg-info me-1">' + peakHours.split(', ').join('</span> <span class="badge bg-info me-1">') + '</span>';
}

// Функция для обновления badge множителя
function updateMultiplierBadge(elementId, baseValue, currentValue, limitsDisabled = false) {
    const element = document.getElementById(elementId);

    if (limitsDisabled) {
        element.textContent = '∞';
        element.className = 'badge bg-danger';
        return;
    }

    const multiplier = currentValue / baseValue;

    if (multiplier === 1) {
        element.textContent = '1x';
        element.className = 'badge bg-secondary';
    } else if (multiplier === 1.5) {
        element.textContent = '1.5x';
        element.className = 'badge bg-info';
    } else if (multiplier === 2) {
        element.textContent = '2x';
        element.className = 'badge bg-warning';
    } else {
        element.textContent = multiplier.toFixed(1) + 'x';
        element.className = 'badge bg-primary';
    }
}

// Функция для обновления лимитов
function refreshLimits() {
    loadLimitsInfo();
}

// Функция подтверждения для переключения лимитов
function confirmToggleLimits(checkbox) {
    const newState = checkbox.checked;

    if (newState) {
        if (!confirm('Вы уверены, что хотите ОТКЛЮЧИТЬ все лимиты рекламы?\n\nЭто позволит пользователям смотреть неограниченное количество рекламы.')) {
            checkbox.checked = false;
            return false;
        }
    }

    return true;
}

// Функция для тестирования сессии
function testSession() {
    fetch('test_session.php', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Данные сессии:', data);
        alert('Данные сессии выведены в консоль браузера (F12)');
    })
    .catch(error => {
        console.error('Ошибка тестирования сессии:', error);
        alert('Ошибка тестирования сессии: ' + error.message);
    });
}

// Загружаем данные при переключении на вкладку лимитов
document.addEventListener('DOMContentLoaded', function() {
    const limitsTab = document.getElementById('limits-tab');
    if (limitsTab) {
        limitsTab.addEventListener('shown.bs.tab', function() {
            loadLimitsSettings(); // Загружаем настройки переключателя (из встроенных данных)
            loadLimitsInfo();     // Затем загружаем информацию о лимитах
        });
    }

    // Автообновление каждые 30 секунд, если вкладка активна
    setInterval(function() {
        const limitsPane = document.getElementById('limits');
        if (limitsPane && limitsPane.classList.contains('active')) {
            loadLimitsInfo();     // Обновляем только лимиты (настройки не меняются без перезагрузки)
        }
    }, 30000);

    // Обработчик для антифрод вкладки
    const antifraudTab = document.getElementById('antifraud-tab');
    if (antifraudTab) {
        antifraudTab.addEventListener('shown.bs.tab', function() {
            loadAntifraudData();
            loadAntifraudSettings();
        });
    }

    // Обработчик формы настроек антифрод
    const antifraudForm = document.getElementById('antifraud-settings-form');
    if (antifraudForm) {
        antifraudForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveAntifraudSettings();
        });
    }
});

// === ФУНКЦИИ ДЛЯ АНТИФРОД СИСТЕМЫ ===

// Загрузка настроек антифрод системы
function loadAntifraudSettings() {
    console.log('Загрузка настроек антифрод системы...');

    fetch('../fraud-detection-simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_admin_settings'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.settings) {
            updateAntifraudSettingsForm(data.settings);
        } else {
            console.error('Ошибка загрузки настроек антифрод:', data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса настроек антифрод:', error);
    });
}

// Обновление формы настроек
function updateAntifraudSettingsForm(settings) {
    document.getElementById('enable-antifraud').checked = settings.enable_antifraud || false;
    document.getElementById('fraud-threshold').value = settings.fraud_threshold || 50;
    document.getElementById('block-vpn').checked = settings.block_vpn || false;
    document.getElementById('vpn-threshold').value = settings.vpn_threshold || 70;
    document.getElementById('block-duplicate-fingerprints').checked = settings.block_duplicate_fingerprints || false;
    document.getElementById('block-self-referrals').checked = settings.block_self_referrals || false;

    // Обновляем статусный бейдж
    const statusBadge = document.getElementById('antifraud-status-badge');
    if (statusBadge) {
        if (settings.enable_antifraud) {
            statusBadge.textContent = 'Активна';
            statusBadge.className = 'badge bg-success';
        } else {
            statusBadge.textContent = 'Отключена';
            statusBadge.className = 'badge bg-danger';
        }
    }
}

// Загрузка данных антифрод системы
function loadAntifraudData() {
    console.log('Загрузка данных антифрод системы...');

    // Показываем индикаторы загрузки
    document.getElementById('antifraud-stats-loading').style.display = 'block';
    document.getElementById('antifraud-stats-content').style.display = 'none';
    document.getElementById('fraud-log-loading').style.display = 'block';
    document.getElementById('fraud-log-content').style.display = 'none';
    document.getElementById('blocked-devices-loading').style.display = 'block';
    document.getElementById('blocked-devices-content').style.display = 'none';

    // Загружаем статистику
    fetch('../fraud-detection-simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_admin_stats'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAntifraudStats(data.stats);
            updateFraudLog(data.fraud_log);
            updateBlockedDevices(data.blocked_devices);
        } else {
            console.error('Ошибка загрузки данных антифрод:', data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса антифрод данных:', error);
    });
}

// Обновление статистики антифрод
function updateAntifraudStats(stats) {
    document.getElementById('total-fingerprints').textContent = stats.total_fingerprints || 0;
    document.getElementById('blocked-devices').textContent = stats.blocked_devices || 0;
    document.getElementById('duplicate-fingerprints').textContent = stats.duplicate_fingerprints || 0;
    document.getElementById('fraud-attempts').textContent = stats.fraud_attempts || 0;
    document.getElementById('vpn-detections').textContent = stats.vpn_detections || 0;
    document.getElementById('vpn-blocked').textContent = stats.vpn_blocked || 0;

    // Скрываем загрузку и показываем контент
    document.getElementById('antifraud-stats-loading').style.display = 'none';
    document.getElementById('antifraud-stats-content').style.display = 'block';
}

// Обновление журнала фрода
function updateFraudLog(fraudLog) {
    const tableBody = document.getElementById('fraud-log-table');
    tableBody.innerHTML = '';

    if (!fraudLog || fraudLog.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Нет записей о фроде</td></tr>';
    } else {
        fraudLog.forEach(entry => {
            const row = document.createElement('tr');

            // Определяем класс для уровня риска
            let riskClass = 'badge bg-secondary';
            if (entry.risk_level === 'CRITICAL') riskClass = 'badge bg-danger';
            else if (entry.risk_level === 'HIGH') riskClass = 'badge bg-warning';
            else if (entry.risk_level === 'MEDIUM') riskClass = 'badge bg-info';
            else if (entry.risk_level === 'LOW') riskClass = 'badge bg-success';

            row.innerHTML = `
                <td>${new Date(entry.timestamp).toLocaleString('ru-RU')}</td>
                <td>${entry.user_id || 'Неизвестен'}</td>
                <td>${entry.violation_type || 'Неизвестно'}</td>
                <td><span class="${riskClass}">${entry.risk_level || 'UNKNOWN'}</span></td>
                <td>${entry.risk_score || 0}</td>
                <td>${entry.action || 'Нет действия'}</td>
                <td><small>${entry.details || 'Нет деталей'}</small></td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Скрываем загрузку и показываем контент
    document.getElementById('fraud-log-loading').style.display = 'none';
    document.getElementById('fraud-log-content').style.display = 'block';
}

// Обновление списка заблокированных устройств
function updateBlockedDevices(blockedDevices) {
    const tableBody = document.getElementById('blocked-devices-table');
    tableBody.innerHTML = '';

    if (!blockedDevices || blockedDevices.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">Нет заблокированных устройств</td></tr>';
    } else {
        blockedDevices.forEach(device => {
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><code>${device.fingerprint.substring(0, 16)}...</code></td>
                <td>${device.user_count || 0} пользователей</td>
                <td>${new Date(device.blocked_at).toLocaleString('ru-RU')}</td>
                <td>${device.reason || 'Неизвестно'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="unblockDevice('${device.fingerprint}')">
                        <i class="fas fa-unlock"></i> Разблокировать
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Скрываем загрузку и показываем контент
    document.getElementById('blocked-devices-loading').style.display = 'none';
    document.getElementById('blocked-devices-content').style.display = 'block';
}

// Сохранение настроек антифрод
function saveAntifraudSettings() {
    const settings = {
        enable_antifraud: document.getElementById('enable-antifraud').checked,
        fraud_threshold: parseInt(document.getElementById('fraud-threshold').value),
        block_vpn: document.getElementById('block-vpn').checked,
        vpn_threshold: parseInt(document.getElementById('vpn-threshold').value),
        block_duplicate_fingerprints: document.getElementById('block-duplicate-fingerprints').checked,
        block_self_referrals: document.getElementById('block-self-referrals').checked
    };

    fetch('../fraud-detection-simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'save_admin_settings',
            settings: settings
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Настройки антифрод системы сохранены!');
        } else {
            alert('Ошибка сохранения настроек: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка сохранения настроек:', error);
        alert('Ошибка сохранения настроек');
    });
}

// Обновление журнала фрода
function refreshFraudLog() {
    loadAntifraudData();
}

// Очистка журнала фрода
function clearFraudLog() {
    if (confirm('Вы уверены, что хотите очистить журнал фрода?')) {
        fetch('../fraud-detection-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'clear_fraud_log'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Журнал фрода очищен!');
                loadAntifraudData();
            } else {
                alert('Ошибка очистки журнала: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка очистки журнала:', error);
            alert('Ошибка очистки журнала');
        });
    }
}

// Разблокировка устройства
function unblockDevice(fingerprint) {
    if (confirm('Вы уверены, что хотите разблокировать это устройство?')) {
        fetch('../fraud-detection-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'unblock_device',
                fingerprint: fingerprint
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Устройство разблокировано!');
                loadAntifraudData();
            } else {
                alert('Ошибка разблокировки устройства: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка разблокировки устройства:', error);
            alert('Ошибка разблокировки устройства');
        });
    }
}

// === ФУНКЦИИ ДЛЯ ОТПЕЧАТКОВ УСТРОЙСТВ ===

let currentFingerprintData = [];
let currentFingerprintPage = 1;
const fingerprintsPerPage = 20;

// Обработчик для таба отпечатков
document.addEventListener('DOMContentLoaded', function() {
    const fingerprintsTab = document.getElementById('fingerprints-tab');
    if (fingerprintsTab) {
        fingerprintsTab.addEventListener('shown.bs.tab', function() {
            loadFingerprints();
        });
    }

    // Обработчики поиска и фильтров
    const searchInput = document.getElementById('fingerprint-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(applyFingerprintFilters, 300));
    }
});

// Загрузка отпечатков устройств
function loadFingerprints() {
    console.log('Загрузка отпечатков устройств...');

    // Показываем индикатор загрузки
    document.getElementById('fingerprints-loading').style.display = 'block';
    document.getElementById('fingerprints-content').style.display = 'none';

    fetch('../fraud-detection-simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_all_fingerprints'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentFingerprintData = data.fingerprints || [];
            updateFingerprintsCount(currentFingerprintData.length);
            applyFingerprintFilters();
        } else {
            console.error('Ошибка загрузки отпечатков:', data.error);
            showFingerprintsError('Ошибка загрузки отпечатков: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса отпечатков:', error);
        showFingerprintsError('Ошибка подключения к серверу');
    });
}

// Обновление счетчика отпечатков
function updateFingerprintsCount(count) {
    const badge = document.getElementById('fingerprints-count-badge');
    if (badge) {
        badge.textContent = count;
    }
}

// Применение фильтров и поиска
function applyFingerprintFilters() {
    const searchTerm = document.getElementById('fingerprint-search').value.toLowerCase();
    const filterType = document.getElementById('fingerprint-filter').value;
    const sortType = document.getElementById('fingerprint-sort').value;

    let filteredData = [...currentFingerprintData];

    // Применяем поиск
    if (searchTerm) {
        filteredData = filteredData.filter(item =>
            item.user_id.toString().toLowerCase().includes(searchTerm) ||
            item.fingerprint.toLowerCase().includes(searchTerm) ||
            (item.ip_address && item.ip_address.toLowerCase().includes(searchTerm))
        );
    }

    // Применяем фильтр
    if (filterType) {
        filteredData = filteredData.filter(item => {
            switch (filterType) {
                case 'blocked':
                    return item.blocked || false;
                case 'suspicious':
                    return (item.risk_score || 0) > 50;
                case 'duplicates':
                    return (item.duplicate_count || 0) > 1;
                default:
                    return true;
            }
        });
    }

    // Применяем сортировку
    filteredData.sort((a, b) => {
        switch (sortType) {
            case 'date_desc':
                return (b.timestamp || 0) - (a.timestamp || 0);
            case 'date_asc':
                return (a.timestamp || 0) - (b.timestamp || 0);
            case 'user_id':
                return a.user_id.toString().localeCompare(b.user_id.toString());
            case 'risk_score':
                return (b.risk_score || 0) - (a.risk_score || 0);
            default:
                return 0;
        }
    });

    displayFingerprints(filteredData);
}

// Отображение отпечатков в таблице
function displayFingerprints(data) {
    const tableBody = document.getElementById('fingerprints-table');
    tableBody.innerHTML = '';

    if (!data || data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Нет отпечатков устройств</td></tr>';
    } else {
        // Пагинация
        const startIndex = (currentFingerprintPage - 1) * fingerprintsPerPage;
        const endIndex = startIndex + fingerprintsPerPage;
        const pageData = data.slice(startIndex, endIndex);

        pageData.forEach(item => {
            const row = document.createElement('tr');

            // Определяем статус
            let statusBadge = '<span class="badge bg-success">Активен</span>';
            if (item.blocked) {
                statusBadge = '<span class="badge bg-danger">Заблокирован</span>';
            } else if ((item.risk_score || 0) > 50) {
                statusBadge = '<span class="badge bg-warning">Подозрительный</span>';
            }

            // Определяем цвет риск-скора
            const riskScore = item.risk_score || 0;
            let riskClass = 'text-success';
            if (riskScore > 70) riskClass = 'text-danger';
            else if (riskScore > 40) riskClass = 'text-warning';

            row.innerHTML = `
                <td><strong>${item.user_id}</strong></td>
                <td><code>${item.fingerprint.substring(0, 16)}...</code></td>
                <td>${item.timestamp ? new Date(item.timestamp * 1000).toLocaleString('ru-RU') : 'Неизвестно'}</td>
                <td>${item.ip_address || 'Неизвестно'}</td>
                <td><span class="${riskClass}">${riskScore}</span></td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewFingerprintDetails('${item.fingerprint}', '${item.user_id}')">
                        <i class="fas fa-eye"></i> Детали
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Обновляем пагинацию
        updateFingerprintsPagination(data.length);
    }

    // Скрываем загрузку и показываем контент
    document.getElementById('fingerprints-loading').style.display = 'none';
    document.getElementById('fingerprints-content').style.display = 'block';
}

// Обновление пагинации
function updateFingerprintsPagination(totalItems) {
    const totalPages = Math.ceil(totalItems / fingerprintsPerPage);
    const pagination = document.getElementById('fingerprints-pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return;

    // Предыдущая страница
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentFingerprintPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changeFingerprintPage(${currentFingerprintPage - 1})">Предыдущая</a>`;
    pagination.appendChild(prevLi);

    // Номера страниц
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentFingerprintPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changeFingerprintPage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // Следующая страница
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentFingerprintPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changeFingerprintPage(${currentFingerprintPage + 1})">Следующая</a>`;
    pagination.appendChild(nextLi);
}

// Изменение страницы
function changeFingerprintPage(page) {
    if (page < 1) return;
    currentFingerprintPage = page;
    applyFingerprintFilters();
}

// Просмотр деталей отпечатка
function viewFingerprintDetails(fingerprint, userId) {
    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_fingerprint_details',
            fingerprint: fingerprint,
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showFingerprintModal(data.details);
        } else {
            alert('Ошибка загрузки деталей: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса деталей:', error);
        alert('Ошибка подключения к серверу');
    });
}

// Показ модального окна с деталями
function showFingerprintModal(details) {
    const modalBody = document.getElementById('fingerprintModalBody');

    let componentsHtml = '';
    if (details.components) {
        componentsHtml = '<h6>Компоненты отпечатка:</h6><pre class="bg-light p-2 rounded">' +
                        JSON.stringify(details.components, null, 2) + '</pre>';
    }

    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Основная информация:</h6>
                <table class="table table-sm">
                    <tr><td><strong>User ID:</strong></td><td>${details.user_id}</td></tr>
                    <tr><td><strong>Отпечаток:</strong></td><td><code>${details.fingerprint}</code></td></tr>
                    <tr><td><strong>IP адрес:</strong></td><td>${details.ip_address || 'Неизвестно'}</td></tr>
                    <tr><td><strong>Дата регистрации:</strong></td><td>${details.timestamp ? new Date(details.timestamp * 1000).toLocaleString('ru-RU') : 'Неизвестно'}</td></tr>
                    <tr><td><strong>Риск-скор:</strong></td><td><span class="badge bg-${details.risk_score > 50 ? 'danger' : 'success'}">${details.risk_score || 0}</span></td></tr>
                    <tr><td><strong>Статус:</strong></td><td>${details.blocked ? '<span class="badge bg-danger">Заблокирован</span>' : '<span class="badge bg-success">Активен</span>'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                ${componentsHtml}
            </div>
        </div>
    `;

    // Сохраняем текущий отпечаток для блокировки
    window.currentFingerprintForBlock = details.fingerprint;

    // Показываем модальное окно
    const modal = new bootstrap.Modal(document.getElementById('fingerprintModal'));
    modal.show();
}

// Обновление отпечатков
function refreshFingerprints() {
    loadFingerprints();
}

// Экспорт отпечатков
function exportFingerprints() {
    const dataStr = JSON.stringify(currentFingerprintData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fingerprints_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
}

// Блокировка отпечатка
function blockFingerprint() {
    if (!window.currentFingerprintForBlock) return;

    if (confirm('Вы уверены, что хотите заблокировать это устройство?')) {
        fetch('../fraud-detection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'block_device',
                fingerprint: window.currentFingerprintForBlock,
                reason: 'Заблокировано администратором'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Устройство заблокировано!');
                bootstrap.Modal.getInstance(document.getElementById('fingerprintModal')).hide();
                loadFingerprints();
            } else {
                alert('Ошибка блокировки: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка блокировки:', error);
            alert('Ошибка подключения к серверу');
        });
    }
}

// Показ ошибки загрузки отпечатков
function showFingerprintsError(message) {
    document.getElementById('fingerprints-loading').style.display = 'none';
    document.getElementById('fingerprints-content').innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
        </div>
    `;
    document.getElementById('fingerprints-content').style.display = 'block';
}

// Debounce функция для поиска
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// === ФУНКЦИИ ДЛЯ МОНИТОРИНГА И СТАТИСТИКИ ===

let monitoringInterval = null;
let threatsChart = null;
let typesChart = null;

// Обработчик для таба мониторинга
document.addEventListener('DOMContentLoaded', function() {
    const monitoringTab = document.getElementById('monitoring-tab');
    if (monitoringTab) {
        monitoringTab.addEventListener('shown.bs.tab', function() {
            loadMonitoringData();
            startMonitoringUpdates();
        });

        monitoringTab.addEventListener('hidden.bs.tab', function() {
            stopMonitoringUpdates();
        });
    }
});

// Загрузка данных мониторинга
function loadMonitoringData() {
    console.log('Загрузка данных мониторинга...');

    const period = document.getElementById('stats-period').value;

    // Загружаем основную статистику
    loadFraudStats(period);

    // Загружаем данные для графиков
    loadChartsData(period);

    // Загружаем топ угроз
    loadTopThreats(period);

    // Загружаем статистику в реальном времени
    loadRealTimeStats();
}

// Загрузка основной статистики
function loadFraudStats(period = '24h') {
    fetch(`../fraud-stats.php?action=get_stats&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatsDisplay(data.stats);
            } else {
                console.error('Ошибка загрузки статистики:', data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка запроса статистики:', error);
        });
}

// Обновление отображения статистики
function updateStatsDisplay(stats) {
    document.getElementById('fraud-attempts').textContent = stats.fraud_attempts || 0;
    document.getElementById('vpn-detections').textContent = stats.vpn_detections || 0;
    document.getElementById('blocked-users').textContent = stats.blocked_users || 0;
    document.getElementById('success-rate').textContent = (stats.success_rate || 0) + '%';

    // Обновляем бейдж в табе
    const threatsCount = (stats.fraud_attempts || 0) + (stats.vpn_detections || 0);
    document.getElementById('threats-count-badge').textContent = threatsCount;
}

// Загрузка данных для графиков
function loadChartsData(period = '24h') {
    fetch(`../fraud-stats.php?action=get_charts_data&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCharts(data.charts);
            } else {
                console.error('Ошибка загрузки данных графиков:', data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка запроса данных графиков:', error);
        });
}

// Обновление графиков
function updateCharts(chartsData) {
    // График активности по времени
    updateThreatsTimelineChart(chartsData.timeline);

    // График типов угроз
    updateThreatTypesChart(chartsData.threat_types);
}

// Обновление графика активности угроз
function updateThreatsTimelineChart(timelineData) {
    const ctx = document.getElementById('threats-timeline-chart').getContext('2d');

    if (threatsChart) {
        threatsChart.destroy();
    }

    threatsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: timelineData.labels,
            datasets: [
                {
                    label: 'Попытки фрода',
                    data: timelineData.data.map(d => d.fraud_attempts),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                },
                {
                    label: 'VPN детекции',
                    data: timelineData.data.map(d => d.vpn_detections),
                    borderColor: 'rgb(255, 205, 86)',
                    backgroundColor: 'rgba(255, 205, 86, 0.2)',
                    tension: 0.1
                },
                {
                    label: 'Всего угроз',
                    data: timelineData.data.map(d => d.total_threats),
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Активность угроз по времени'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Обновление графика типов угроз
function updateThreatTypesChart(threatTypesData) {
    const ctx = document.getElementById('threat-types-chart').getContext('2d');

    if (typesChart) {
        typesChart.destroy();
    }

    typesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: threatTypesData.labels,
            datasets: [{
                data: threatTypesData.data,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Распределение типов угроз'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Загрузка топ угроз
function loadTopThreats(period = '24h') {
    fetch(`../fraud-stats.php?action=get_top_threats&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTopThreatsTable(data.top_threats);
            } else {
                console.error('Ошибка загрузки топ угроз:', data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка запроса топ угроз:', error);
        });
}

// Обновление таблицы топ угроз
function updateTopThreatsTable(threats) {
    const tableBody = document.getElementById('top-threats-table');
    tableBody.innerHTML = '';

    if (!threats || threats.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Нет данных за выбранный период</td></tr>';
        return;
    }

    threats.forEach(threat => {
        const row = document.createElement('tr');

        // Определяем цвет риск-скора
        let riskClass = 'text-success';
        if (threat.risk_score >= 80) riskClass = 'text-danger';
        else if (threat.risk_score >= 60) riskClass = 'text-warning';

        row.innerHTML = `
            <td><strong>${threat.user_id}</strong></td>
            <td><span class="badge bg-danger">${threat.reason}</span></td>
            <td><span class="${riskClass}">${threat.risk_score}</span></td>
            <td>${threat.ip_address}</td>
            <td><code>${threat.device_fingerprint}</code></td>
            <td>${threat.blocked_at}</td>
        `;

        tableBody.appendChild(row);
    });
}

// Загрузка статистики в реальном времени
function loadRealTimeStats() {
    fetch('../fraud-stats.php?action=get_real_time_stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSystemStatus(data.real_time);
            } else {
                console.error('Ошибка загрузки статистики в реальном времени:', data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка запроса статистики в реальном времени:', error);
        });
}

// Обновление статуса системы
function updateSystemStatus(realTimeStats) {
    const statusDiv = document.getElementById('system-status');
    const activeThreatsSpan = document.getElementById('active-threats');
    const lastUpdateSpan = document.getElementById('last-update');

    // Обновляем активные угрозы
    activeThreatsSpan.textContent = realTimeStats.active_threats || 0;

    // Обновляем время последнего обновления
    lastUpdateSpan.textContent = new Date().toLocaleTimeString('ru-RU');

    // Обновляем статус системы
    let statusClass = 'alert-success';
    let statusIcon = 'fas fa-check-circle';
    let statusText = 'Система работает нормально';

    switch (realTimeStats.system_status) {
        case 'high_load':
            statusClass = 'alert-danger';
            statusIcon = 'fas fa-exclamation-triangle';
            statusText = 'Высокая нагрузка - обнаружено много угроз';
            break;
        case 'moderate_load':
            statusClass = 'alert-warning';
            statusIcon = 'fas fa-exclamation-circle';
            statusText = 'Умеренная нагрузка - повышенная активность угроз';
            break;
    }

    statusDiv.className = `alert ${statusClass}`;
    statusDiv.innerHTML = `<i class="${statusIcon}"></i> ${statusText}`;
}

// Запуск автоматических обновлений
function startMonitoringUpdates() {
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
    }

    // Обновляем каждые 30 секунд
    monitoringInterval = setInterval(() => {
        loadRealTimeStats();
    }, 30000);
}

// Остановка автоматических обновлений
function stopMonitoringUpdates() {
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
        monitoringInterval = null;
    }
}

// Обновление периода статистики
function updateStatsPeriod() {
    loadMonitoringData();
}

// Принудительное обновление мониторинга
function refreshMonitoring() {
    loadMonitoringData();
}

</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
