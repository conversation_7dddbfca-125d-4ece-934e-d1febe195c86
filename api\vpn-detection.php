<?php
/**
 * api/vpn-detection.php
 * Серверная система детекции VPN/Proxy
 * 
 * Анализирует IP адреса, заголовки и другие данные
 * для обнаружения VPN/Proxy соединений
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/config.php';

error_log("vpn-detection INFO: Получен запрос на анализ VPN");

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('Отсутствует параметр action');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'analyze_vpn':
            echo json_encode(analyzeVPN($input));
            break;
            
        case 'check_ip_reputation':
            echo json_encode(checkIPReputation($input));
            break;
            
        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log('vpn-detection ERROR: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'vpn_detected' => false,
        'risk_score' => 0
    ]);
}

/**
 * Основная функция анализа VPN/Proxy
 */
function analyzeVPN($input) {
    try {
        $clientIP = getRealIpAddr();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $clientData = $input['client_data'] ?? [];
        
        $analysis = [
            'ip_analysis' => analyzeIP($clientIP),
            'header_analysis' => analyzeHeaders(),
            'geolocation_analysis' => analyzeGeolocation($clientIP, $clientData),
            'reputation_analysis' => checkIPDatabase($clientIP),
            'client_analysis' => $clientData
        ];
        
        // Вычисляем общий риск-скор
        $riskScoreResult = calculateVPNRiskScore($analysis);
        $riskScore = $riskScoreResult['score'];
        $riskDetails = $riskScoreResult['details'];

        // ЛОГИРУЕМ РИСК-СКОР
        error_log("analyzeVPN DEBUG: Calculated Risk Score: $riskScore");
        error_log("analyzeVPN DEBUG: Risk Details: " . implode(', ', $riskDetails));
        error_log("analyzeVPN DEBUG: VPN Detected: " . ($riskScore >= 50 ? 'YES' : 'NO'));

        // Логируем результат
        logVPNAnalysis($clientIP, $analysis, $riskScore);

        $vpnDetected = $riskScore >= 50;
        $shouldBlock = false;

        // 🔒 АВТОМАТИЧЕСКАЯ БЛОКИРОВКА ПРИ ОБНАРУЖЕНИИ VPN
        if ($vpnDetected) {
            $shouldBlock = autoBlockVPNUser($input, $clientIP, $riskScore, $analysis);
        }

        return [
            'success' => true,
            'vpn_detected' => $vpnDetected,
            'risk_score' => $riskScore,
            'analysis' => $analysis,
            'client_ip' => $clientIP,
            'blocked' => $shouldBlock,
            'timestamp' => time()
        ];
        
    } catch (Exception $e) {
        error_log('analyzeVPN ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'vpn_detected' => false,
            'risk_score' => 0
        ];
    }
}

/**
 * Анализ IP адреса
 */
function analyzeIP($ip) {
    $analysis = [
        'ip' => $ip,
        'is_private' => isPrivateIP($ip),
        'is_reserved' => isReservedIP($ip),
        'reverse_dns' => null,
        'asn_info' => null
    ];
    
    // Reverse DNS lookup
    try {
        $hostname = gethostbyaddr($ip);
        if ($hostname !== $ip) {
            $analysis['reverse_dns'] = $hostname;
            
            // Проверяем подозрительные паттерны в hostname
            $vpnPatterns = [
                '/vpn/i', '/proxy/i', '/tor/i', '/tunnel/i',
                '/anonymous/i', '/private/i', '/secure/i'
            ];
            
            foreach ($vpnPatterns as $pattern) {
                if (preg_match($pattern, $hostname)) {
                    $analysis['suspicious_hostname'] = true;
                    break;
                }
            }
        }
    } catch (Exception $e) {
        $analysis['reverse_dns_error'] = $e->getMessage();
    }
    
    return $analysis;
}

/**
 * Анализ заголовков HTTP
 */
function analyzeHeaders() {
    $analysis = [
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
        'proxy_headers' => [],
        'suspicious_user_agent' => false,
        'vpn_indicators' => []
    ];

    // Расширенный список заголовков, указывающих на прокси/VPN
    $proxyHeaders = [
        'HTTP_VIA',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED',
        'HTTP_X_CLUSTER_CLIENT_IP',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'HTTP_CLIENT_IP',
        'HTTP_X_REAL_IP',
        'HTTP_X_ORIGINATING_IP',
        'HTTP_X_REMOTE_IP',
        'HTTP_X_REMOTE_ADDR',
        'HTTP_CF_CONNECTING_IP', // Cloudflare
        'HTTP_TRUE_CLIENT_IP',
        'HTTP_X_PROXY_ID',
        'HTTP_X_PROXY_CONNECTION',
        'HTTP_PROXY_CONNECTION',
        'HTTP_X_COMING_FROM',
        'HTTP_X_REWRITE_URL',
        'HTTP_X_BLUECOAT_VIA'
    ];

    foreach ($proxyHeaders as $header) {
        if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
            $analysis['proxy_headers'][$header] = $_SERVER[$header];
        }
    }

    // Анализ User Agent на подозрительные паттерны
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    if ($userAgent) {
        $suspiciousPatterns = [
            '/curl/i', '/wget/i', '/python/i', '/java/i', '/go-http/i',
            '/bot/i', '/crawler/i', '/spider/i', '/scraper/i',
            '/headless/i', '/phantom/i', '/selenium/i', '/webdriver/i',
            '/automation/i', '/test/i', '/monitor/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $analysis['suspicious_user_agent'] = true;
                $analysis['vpn_indicators'][] = 'suspicious_user_agent';
                break;
            }
        }

        // Проверяем на VPN клиенты в User Agent
        $vpnPatterns = [
            '/vpn/i', '/proxy/i', '/tunnel/i', '/tor/i', '/onion/i'
        ];

        foreach ($vpnPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $analysis['vpn_indicators'][] = 'vpn_in_user_agent';
                break;
            }
        }
    }

    // Проверяем специфические заголовки VPN сервисов
    $vpnServiceHeaders = [
        'HTTP_X_NORDVPN',
        'HTTP_X_EXPRESSVPN',
        'HTTP_X_SURFSHARK',
        'HTTP_X_VPN_CLIENT',
        'HTTP_X_PROXY_CLIENT',
        'HTTP_X_TUNNEL_CLIENT'
    ];

    foreach ($vpnServiceHeaders as $header) {
        if (isset($_SERVER[$header])) {
            $analysis['vpn_indicators'][] = 'vpn_service_header';
            break;
        }
    }

    return $analysis;
}

/**
 * Анализ геолокации
 */
function analyzeGeolocation($ip, $clientData) {
    $analysis = [
        'server_ip_geo' => getIPGeolocation($ip),
        'client_timezone' => $clientData['timezone'] ?? null,
        'client_geo' => $clientData['geolocation'] ?? null,
        'mismatch_detected' => false
    ];
    
    // Сравниваем timezone с IP геолокацией
    if ($analysis['server_ip_geo'] && $analysis['client_timezone']) {
        $ipCountry = $analysis['server_ip_geo']['country'] ?? '';
        $timezoneCountry = getCountryFromTimezone($analysis['client_timezone']);
        
        if ($ipCountry && $timezoneCountry && $ipCountry !== $timezoneCountry) {
            $analysis['mismatch_detected'] = true;
            $analysis['mismatch_details'] = [
                'ip_country' => $ipCountry,
                'timezone_country' => $timezoneCountry
            ];
        }
    }
    
    return $analysis;
}

/**
 * Проверка IP в базах данных
 */
function checkIPDatabase($ip) {
    $analysis = [
        'checked' => false,
        'is_malicious' => false,
        'is_vpn' => false,
        'confidence' => 0,
        'country' => null,
        'isp' => null,
        'sources' => []
    ];

    // Проверка через ip-api.com (бесплатный сервис)
    try {
        $url = "http://ip-api.com/json/{$ip}?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,proxy,hosting,query";
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'VPN Detection Service'
            ]
        ]);

        $response = file_get_contents($url, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['status'] === 'success') {
                $analysis['checked'] = true;
                $analysis['country'] = $data['country'] ?? null;
                $analysis['isp'] = $data['isp'] ?? null;
                $analysis['is_proxy'] = $data['proxy'] ?? false;
                $analysis['is_hosting'] = $data['hosting'] ?? false;
                $analysis['sources'][] = 'ip-api.com';
                $analysis['raw_data'] = $data;

                // Если это хостинг или прокси - повышаем подозрения
                if ($data['proxy'] || $data['hosting']) {
                    $analysis['is_vpn'] = true;
                    $analysis['confidence'] = 80;
                }

                // Расширенная проверка ISP на VPN паттерны
                $vpnISPPatterns = [
                    '/vpn/i', '/proxy/i', '/tunnel/i', '/private/i',
                    '/secure/i', '/anonymous/i', '/nordvpn/i', '/expressvpn/i',
                    '/surfshark/i', '/cyberghost/i', '/adguard/i', '/protonvpn/i',
                    '/mullvad/i', '/windscribe/i', '/purevpn/i', '/hotspot shield/i',
                    '/hide\.me/i', '/ipvanish/i', '/tunnelbear/i', '/zenmate/i',
                    '/hola/i', '/betternet/i', '/opera vpn/i', '/psiphon/i',
                    '/tor/i', '/onion/i', '/relay/i', '/exit node/i',
                    '/datacenter/i', '/hosting/i', '/cloud/i', '/server/i',
                    '/digital ocean/i', '/amazon/i', '/google cloud/i', '/azure/i',
                    '/linode/i', '/vultr/i', '/ovh/i', '/hetzner/i'
                ];

                foreach ($vpnISPPatterns as $pattern) {
                    if (preg_match($pattern, $data['isp'] ?? '') || preg_match($pattern, $data['org'] ?? '')) {
                        $analysis['is_vpn'] = true;
                        $analysis['confidence'] = 90;
                        $analysis['vpn_pattern_match'] = true;
                        $analysis['matched_pattern'] = $pattern;
                        break;
                    }
                }
            }
        }
    } catch (Exception $e) {
        error_log('checkIPDatabase ip-api ERROR: ' . $e->getMessage());
    }

    // Расширенная проверка по известным VPN диапазонам
    $knownVPNRanges = [
        '185.220.', // Tor exit nodes
        '199.87.',   // NordVPN
        '103.231.',  // ExpressVPN
        '104.28.',   // Cloudflare (часто используется VPN)
        '172.67.',   // Cloudflare
        '198.41.',   // Cloudflare
        '162.158.',  // Cloudflare
        '45.8.',     // Популярные VPN диапазоны
        '89.187.',   // Европейские VPN
        '91.219.',   // Европейские VPN
        '185.159.',  // VPN провайдеры
        '194.36.',   // VPN провайдеры
    ];

    foreach ($knownVPNRanges as $range) {
        if (strpos($ip, $range) === 0) {
            $analysis['is_vpn'] = true;
            $analysis['confidence'] = max($analysis['confidence'], 85);
            $analysis['range_match'] = $range;
            break;
        }
    }

    return $analysis;
}

/**
 * Вычисление общего риск-скора для VPN
 */
function calculateVPNRiskScore($analysis) {
    $score = 0;
    $details = [];

    // 1. IP анализ (высокий приоритет)
    if ($analysis['ip_analysis']['suspicious_hostname'] ?? false) {
        $score += 40;
        $details[] = 'suspicious_hostname';
    }

    if ($analysis['ip_analysis']['is_private'] ?? false) {
        $score -= 20; // Приватные IP менее подозрительны
        $details[] = 'private_ip';
    }

    // 2. Анализ заголовков (высокий приоритет)
    $proxyHeaders = $analysis['header_analysis']['proxy_headers'] ?? [];
    if (!empty($proxyHeaders)) {
        $headerCount = count($proxyHeaders);
        $score += min($headerCount * 15, 45); // До 45 баллов за заголовки
        $details[] = "proxy_headers_count_{$headerCount}";
    }

    if ($analysis['header_analysis']['suspicious_user_agent'] ?? false) {
        $score += 25;
        $details[] = 'suspicious_user_agent';
    }

    // Дополнительные VPN индикаторы в заголовках
    $vpnIndicators = $analysis['header_analysis']['vpn_indicators'] ?? [];
    if (!empty($vpnIndicators)) {
        $score += count($vpnIndicators) * 10;
        $details[] = 'vpn_indicators_in_headers';
    }

    // 3. Репутация IP (критический фактор)
    $reputation = $analysis['reputation_analysis'];
    if ($reputation['is_vpn'] ?? false) {
        $confidence = $reputation['confidence'] ?? 50;
        $score += $confidence; // Используем confidence как баллы
        $details[] = "ip_reputation_vpn_confidence_{$confidence}";
    }

    if ($reputation['is_proxy'] ?? false) {
        $score += 35;
        $details[] = 'ip_reputation_proxy';
    }

    if ($reputation['is_hosting'] ?? false) {
        $score += 30;
        $details[] = 'ip_reputation_hosting';
    }

    // Проверяем паттерны в ISP
    if ($reputation['vpn_pattern_match'] ?? false) {
        $score += 40;
        $details[] = 'isp_pattern_match';
    }

    // 4. Геолокация (средний приоритет)
    if ($analysis['geolocation_analysis']['mismatch_detected'] ?? false) {
        $score += 25;
        $details[] = 'geo_mismatch';
    }

    // 5. Клиентские данные VPN детекции
    $clientAnalysis = $analysis['client_analysis'];
    if ($clientAnalysis['is_vpn_detected'] ?? false) {
        $clientRisk = $clientAnalysis['risk_score'] ?? 0;
        $score += min($clientRisk, 30); // Ограничиваем влияние клиентского анализа
        $details[] = "client_vpn_risk_{$clientRisk}";
    }

    // 6. Дополнительные проверки

    // Проверяем известные VPN диапазоны
    if ($reputation['range_match'] ?? false) {
        $score += 50;
        $details[] = 'known_vpn_range';
    }

    // Проверяем Cloudflare (часто используется VPN)
    $isp = $reputation['isp'] ?? '';
    if (stripos($isp, 'cloudflare') !== false) {
        $score += 20;
        $details[] = 'cloudflare_detected';
    }

    // Проверяем datacenter/hosting провайдеров
    $hostingPatterns = ['datacenter', 'hosting', 'cloud', 'server', 'digital ocean', 'amazon', 'google cloud', 'azure'];
    foreach ($hostingPatterns as $pattern) {
        if (stripos($isp, $pattern) !== false) {
            $score += 25;
            $details[] = "hosting_provider_{$pattern}";
            break;
        }
    }

    // Логируем детали для отладки
    error_log("VPN Risk Score Calculation: Score=$score, Details=" . implode(', ', $details));

    return [
        'score' => min($score, 100),
        'details' => $details,
        'factors_count' => count($details)
    ];
}

/**
 * Логирование анализа VPN
 */
function logVPNAnalysis($ip, $analysis, $riskScore) {
    try {
        $logFile = __DIR__ . '/../database/vpn_analysis_log.json';
        
        $logEntry = [
            'timestamp' => time(),
            'ip' => $ip,
            'risk_score' => $riskScore,
            'vpn_detected' => $riskScore >= 50,
            'analysis' => $analysis,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $log = [];
        if (file_exists($logFile)) {
            $log = json_decode(file_get_contents($logFile), true) ?: [];
        }
        
        $log[] = $logEntry;
        
        // Ограничиваем размер лога
        if (count($log) > 1000) {
            $log = array_slice($log, -1000);
        }
        
        file_put_contents($logFile, json_encode($log, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
    } catch (Exception $e) {
        error_log('logVPNAnalysis ERROR: ' . $e->getMessage());
    }
}

/**
 * Получение геолокации по IP
 */
function getIPGeolocation($ip) {
    // Простая заглушка - в реальном проекте использовать внешний API
    return [
        'country' => 'Unknown',
        'city' => 'Unknown',
        'latitude' => null,
        'longitude' => null
    ];
}

/**
 * Получение страны по timezone
 */
function getCountryFromTimezone($timezone) {
    $timezoneToCountry = [
        'Europe/Moscow' => 'RU',
        'America/New_York' => 'US',
        'Europe/London' => 'GB',
        'Asia/Tokyo' => 'JP',
        // Добавить больше соответствий
    ];
    
    return $timezoneToCountry[$timezone] ?? null;
}

/**
 * Проверка приватного IP
 */
function isPrivateIP($ip) {
    return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
}

/**
 * Проверка зарезервированного IP
 */
function isReservedIP($ip) {
    return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE);
}

/**
 * Получение реального IP адреса
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        return $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return 'unknown';
}

/**
 * 🔒 Автоматически блокирует пользователя при обнаружении VPN
 */
function autoBlockVPNUser($input, $clientIP, $riskScore, $analysis) {
    try {
        // Проверяем настройки - включена ли автоблокировка VPN
        $settingsFile = __DIR__ . '/../database/antifraud_settings.json';
        $settings = [];

        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true) ?? [];
        }

        $blockVPN = $settings['block_vpn'] ?? true;
        $vpnThreshold = $settings['vpn_threshold'] ?? 70;

        if (!$blockVPN || $riskScore < $vpnThreshold) {
            return false; // Автоблокировка отключена или риск недостаточно высок
        }

        // Извлекаем user_id из initData если есть
        $userId = null;
        if (isset($input['initData'])) {
            $userData = validateTelegramInitData($input['initData']);
            if ($userData) {
                $userId = $userData['id'];
            }
        }

        // Если нет user_id, блокируем по IP
        if (!$userId) {
            $userId = 'ip_' . str_replace('.', '_', $clientIP);
        }

        // Загружаем данные пользователей
        require_once __DIR__ . '/functions.php';
        $allUserData = loadUserData();
        if (!$allUserData) {
            $allUserData = [];
        }

        // Блокируем пользователя
        if (!isset($allUserData[$userId])) {
            $allUserData[$userId] = [];
        }

        $blockReason = 'VPN/Proxy detected (Risk: ' . $riskScore . ')';
        $allUserData[$userId]['blocked'] = true;
        $allUserData[$userId]['fraud_detected'] = true;
        $allUserData[$userId]['block_reason'] = $blockReason;
        $allUserData[$userId]['blocked_at'] = date('Y-m-d H:i:s');
        $allUserData[$userId]['vpn_detected'] = true;
        $allUserData[$userId]['vpn_risk_score'] = $riskScore;
        $allUserData[$userId]['vpn_analysis'] = $analysis;
        $allUserData[$userId]['blocked_ip'] = $clientIP;

        // Сохраняем данные
        $result = saveUserData($allUserData);

        if ($result) {
            // Логируем блокировку
            logVPNBlock($userId, $clientIP, $riskScore, $analysis, $blockReason);

            error_log("VPN AUTO-BLOCK: Пользователь $userId заблокирован за VPN (IP: $clientIP, Risk: $riskScore)");
            return true;
        } else {
            error_log("VPN AUTO-BLOCK ERROR: Не удалось заблокировать пользователя $userId");
            return false;
        }

    } catch (Exception $e) {
        error_log('autoBlockVPNUser ERROR: ' . $e->getMessage());
        return false;
    }
}

/**
 * 📝 Логирует блокировку VPN пользователя
 */
function logVPNBlock($userId, $ip, $riskScore, $analysis, $reason) {
    try {
        $vpnLogFile = __DIR__ . '/../database/vpn_blocks.json';

        $vpnBlocks = [];
        if (file_exists($vpnLogFile)) {
            $vpnBlocks = json_decode(file_get_contents($vpnLogFile), true) ?? [];
        }

        $logEntry = [
            'timestamp' => time(),
            'date' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'ip_address' => $ip,
            'risk_score' => $riskScore,
            'reason' => $reason,
            'analysis' => $analysis,
            'action' => 'blocked'
        ];

        $vpnBlocks[] = $logEntry;

        // Ограничиваем размер лога (последние 1000 записей)
        if (count($vpnBlocks) > 1000) {
            $vpnBlocks = array_slice($vpnBlocks, -1000);
        }

        file_put_contents($vpnLogFile, json_encode($vpnBlocks, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    } catch (Exception $e) {
        error_log('logVPNBlock ERROR: ' . $e->getMessage());
    }
}



?>
